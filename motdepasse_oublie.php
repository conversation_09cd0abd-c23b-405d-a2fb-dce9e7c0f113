<?php
session_start();

require 'vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

$erreur = "";
$message = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $email = trim($_POST['email']);

  if (empty($email)) {
    $erreur = "Veuillez saisir votre adresse e-mail.";
  } else {
    try {
      $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
      $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

      $stmt = $pdo->prepare("SELECT * FROM utilisateurs WHERE email = ?");
      $stmt->execute([$email]);
      $utilisateur = $stmt->fetch(PDO::FETCH_ASSOC);

      if (!$utilisateur) {
        // Pour la sécurité, on affiche le même message même si l'email n'existe pas
        $message = "Si cette adresse email existe, un lien de réinitialisation a été envoyé.";
      } else {
        // Générer un token plus long et plus sécurisé
        $token = bin2hex(random_bytes(32)); // 64 caractères au lieu de 32
        $expire = date('Y-m-d H:i:s', time() + 3600); // 1 heure

        // Nettoyer les anciens tokens de cet utilisateur
        $cleanup = $pdo->prepare("UPDATE utilisateurs SET reset_token = NULL, reset_expire = NULL WHERE id = ?");
        $cleanup->execute([$utilisateur['id']]);

        // Insérer le nouveau token
        $update = $pdo->prepare("UPDATE utilisateurs SET reset_token = ?, reset_expire = ? WHERE id = ?");
        $update->execute([$token, $expire, $utilisateur['id']]);

        // Vérifier que le token a bien été sauvegardé
        $verify = $pdo->prepare("SELECT reset_token FROM utilisateurs WHERE id = ?");
        $verify->execute([$utilisateur['id']]);
        $savedToken = $verify->fetchColumn();

        if ($savedToken !== $token) {
          throw new Exception("Erreur lors de la sauvegarde du token");
        }

        // Construire le lien de réinitialisation
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['REQUEST_URI']);
        $lien = $protocol . '://' . $host . $path . '/reinitialiser_mdp.php?token=' . $token;

        // Log pour debug
        error_log("Token généré: " . $token);
        error_log("Lien généré: " . $lien);
        error_log("Expire le: " . $expire);

        $mail = new PHPMailer(true);

        try {
          $mail->isSMTP();
          $mail->Host = 'smtp.gmail.com';
          $mail->SMTPAuth = true;
          $mail->Username = '<EMAIL>';
          $mail->Password = 'fezy fpmi kptm vwqt';
          $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
          $mail->Port = 587;
          $mail->CharSet = 'UTF-8';

          $mail->setFrom('<EMAIL>', 'Cyber Parc ');
          $mail->addAddress($email, $utilisateur['nom']);

          $mail->isHTML(true);
          $mail->Subject = '🔒 Réinitialisation de votre mot de passe - Cyber Parc';
          $mail->Body = "
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset='UTF-8'>
                        <title>Réinitialisation mot de passe</title>
                    </head>
                    <body style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background: linear-gradient(135deg, #1e3c72, #2a5298); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;'>
                            <h1>🏢 Cyber Parc Djerba</h1>
                            <h2>Réinitialisation de mot de passe</h2>
                        </div>
                        
                        <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;'>
                            <p>Bonjour <strong>" . htmlspecialchars($utilisateur['nom']) . "</strong>,</p>
                            
                            <p>Vous avez demandé la réinitialisation de votre mot de passe.</p>
                            
                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='" . $lien . "' style='background: #1e90ff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;'>
                                    🔒 Réinitialiser mon mot de passe
                                </a>
                            </div>
                            
                            <div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                                <p><strong>⚠️ Important :</strong></p>
                                <ul>
                                    <li>Ce lien expire dans <strong>1 heure</strong></li>
                                    <li>Si vous n'avez pas demandé cette réinitialisation, ignorez cet email</li>
                                </ul>
                            </div>
                            
                            <p>Si le bouton ne fonctionne pas, copiez ce lien dans votre navigateur :</p>
                            <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 5px; font-family: monospace;'>" . $lien . "</p>
                            
                            <hr style='margin: 30px 0; border: none; border-top: 1px solid #ddd;'>
                            <p style='text-align: center; color: #666; font-size: 12px;'>
                                © " . date('Y') . " Cyber Parc Djerba - Tous droits réservés
                            </p>
                        </div>
                    </body>
                    </html>
                    ";

          $mail->send();
          $message = "Un email avec les instructions a été envoyé à votre adresse.";

          // Log succès
          error_log("Email envoyé avec succès à: " . $email);
        } catch (Exception $e) {
          error_log("Erreur PHPMailer: " . $e->getMessage());
          $erreur = "Erreur lors de l'envoi de l'email. Veuillez réessayer.";
        }
      }
    } catch (PDOException $e) {
      error_log("Erreur BDD: " . $e->getMessage());
      $erreur = "Erreur système. Veuillez réessayer plus tard.";
    } catch (Exception $e) {
      error_log("Erreur générale: " . $e->getMessage());
      $erreur = "Erreur système. Veuillez réessayer plus tard.";
    }
  }
}
?>

<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mot de passe oublié - Cyber Parc Djerba</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #1e3c72, #2a5298);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }

    .container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 2.5rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 450px;
    }

    .logo {
      text-align: center;
      margin-bottom: 2rem;
    }

    .logo-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    h1 {
      color: #1e3c72;
      font-size: 1.8rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .subtitle {
      text-align: center;
      color: #6b7280;
      margin-bottom: 2rem;
      font-size: 0.95rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #374151;
    }

    input[type="email"] {
      width: 100%;
      padding: 0.875rem;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      font-size: 1rem;
      transition: all 0.2s ease;
      background: white;
    }

    input[type="email"]:focus {
      outline: none;
      border-color: #1e90ff;
      box-shadow: 0 0 0 3px rgba(30, 144, 255, 0.1);
    }

    .btn {
      width: 100%;
      padding: 1rem;
      background: linear-gradient(135deg, #1e90ff, #1e3c72);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(30, 144, 255, 0.3);
    }

    .btn:active {
      transform: translateY(0);
    }

    .alert {
      padding: 1rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
    }

    .alert-error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .alert-success {
      background: #f0fdf4;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    .back-link {
      text-align: center;
      margin-top: 1.5rem;
    }

    .back-link a {
      color: #6b7280;
      text-decoration: none;
      font-size: 0.9rem;
      transition: color 0.2s ease;
    }

    .back-link a:hover {
      color: #1e90ff;
    }

    .debug-info {
      background: #f3f4f6;
      padding: 1rem;
      border-radius: 8px;
      margin-top: 1rem;
      font-size: 0.8rem;
      color: #6b7280;
      font-family: monospace;
    }

    @media (max-width: 480px) {
      .container {
        padding: 2rem 1.5rem;
      }

      h1 {
        font-size: 1.5rem;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="logo">
      <div class="logo-icon">🏢</div>
      <h1>Mot de passe oublié</h1>
      <p class="subtitle">Entrez votre adresse email pour recevoir un lien de réinitialisation</p>
    </div>

    <?php if ($erreur): ?>
      <div class="alert alert-error">
        <span>⚠️</span>
        <?= htmlspecialchars($erreur) ?>
      </div>
    <?php endif; ?>

    <?php if ($message): ?>
      <div class="alert alert-success">
        <span>✅</span>
        <?= htmlspecialchars($message) ?>
      </div>

    <?php endif; ?>

    <?php if (!$message): ?>
      <form method="post">
        <div class="form-group">
          <label for="email">Adresse e-mail</label>
          <input type="email" id="email" name="email" required
            value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
            placeholder="<EMAIL>">
        </div>

        <button type="submit" class="btn">
          📧 Envoyer le lien de réinitialisation
        </button>
      </form>
    <?php endif; ?>

    <div class="back-link">
      <a href="connexion.php">← Retour à la connexion</a>
    </div>
  </div>
</body>

</html>