<?php
session_start();

// Configuration sécurisée pour la production
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Inclure le fichier de configuration
require_once 'config/database.php';

try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    error_log("Erreur de connexion à la base de données : " . $e->getMessage());
    die("Une erreur est survenue. Veuillez réessayer plus tard.");
}

// Vérification du token dans l'URL
if (!isset($_GET['token']) || empty($_GET['token'])) {
    die("Lien invalide ou expiré.");
}

$token = $_GET['token'];

// Vérifier le token et sa validité
$stmt = $pdo->prepare("SELECT * FROM utilisateurs WHERE reset_token = ? AND reset_expire > NOW()");
$stmt->execute([$token]);
$utilisateur = $stmt->fetch();

if (!$utilisateur) {
    die("Lien invalide ou expiré. Veuillez demander un nouveau lien de réinitialisation.");
}

$erreur = "";
$message = "";

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nouveau_mdp = $_POST['nouveau_mdp'] ?? '';
    $conf_mdp = $_POST['conf_mdp'] ?? '';

    if (empty($nouveau_mdp) || empty($conf_mdp)) {
        $erreur = "Veuillez remplir tous les champs.";
    } elseif ($nouveau_mdp !== $conf_mdp) {
        $erreur = "Les mots de passe ne correspondent pas.";
    } elseif (strlen($nouveau_mdp) < 8) {
        $erreur = "Le mot de passe doit contenir au moins 8 caractères.";
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $nouveau_mdp)) {
        $erreur = "Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre.";
    } else {
        try {
            $hash = password_hash($nouveau_mdp, PASSWORD_DEFAULT);
            $update = $pdo->prepare("UPDATE utilisateurs SET motdepasse = ?, reset_token = NULL, reset_expire = NULL WHERE id = ?");

            if ($update->execute([$hash, $utilisateur['id']])) {
                $message = "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter.";
                // Redirection après 3 secondes
                header("refresh:3;url=index.php");
            } else {
                $erreur = "Erreur lors de la mise à jour du mot de passe.";
            }
        } catch (PDOException $e) {
            error_log("Erreur lors de la réinitialisation du mot de passe : " . $e->getMessage());
            $erreur = "Une erreur est survenue. Veuillez réessayer.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réinitialiser le mot de passe - CyberParc</title>
    <link rel="icon" type="image/x-icon" href="icone.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
        }

        .success {
            background: #efe;
            color: #363;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #363;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .password-requirements {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="logo1.jpg" alt="Logo CyberParc">
        </div>
        
        <h1>Réinitialiser le mot de passe</h1>

        <?php if ($erreur): ?>
            <div class="error"><?= htmlspecialchars($erreur) ?></div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="success"><?= htmlspecialchars($message) ?></div>
        <?php else: ?>
            <form method="POST">
                <div class="form-group">
                    <label for="nouveau_mdp">Nouveau mot de passe :</label>
                    <input type="password" id="nouveau_mdp" name="nouveau_mdp" required>
                    <div class="password-requirements">
                        Le mot de passe doit contenir au moins 8 caractères, une minuscule, une majuscule et un chiffre.
                    </div>
                </div>

                <div class="form-group">
                    <label for="conf_mdp">Confirmer le mot de passe :</label>
                    <input type="password" id="conf_mdp" name="conf_mdp" required>
                </div>

                <button type="submit" class="btn">Réinitialiser le mot de passe</button>
            </form>
        <?php endif; ?>

        <div class="back-link">
            <a href="index.php">← Retour à la connexion</a>
        </div>
    </div>

    <script>
        // Validation en temps réel
        document.getElementById('nouveau_mdp').addEventListener('input', function() {
            const password = this.value;
            const requirements = document.querySelector('.password-requirements');
            
            if (password.length >= 8 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
                requirements.style.color = '#28a745';
                requirements.textContent = '✓ Mot de passe valide';
            } else {
                requirements.style.color = '#666';
                requirements.textContent = 'Le mot de passe doit contenir au moins 8 caractères, une minuscule, une majuscule et un chiffre.';
            }
        });

        // Vérification de la correspondance des mots de passe
        document.getElementById('conf_mdp').addEventListener('input', function() {
            const password = document.getElementById('nouveau_mdp').value;
            const confirm = this.value;
            
            if (confirm && password !== confirm) {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#e1e5e9';
            }
        });
    </script>
</body>
</html>
