@echo off
chcp 65001 >nul
color 0E
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              📦 CRÉATION PACKAGE ICI 📦                     ║
echo ║           Dans le dossier courant du projet                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 Ce script va créer le package dans le dossier courant
echo 📍 Emplacement : %CD%\CYBERPARC_CLIENT_PACKAGE
echo.

:: Vérifier qu'on est dans le bon dossier
if not exist "index.php" (
    echo ❌ ERREUR : Vous n'êtes pas dans le dossier du projet CyberParc !
    echo.
    echo 💡 Assurez-vous d'être dans le dossier contenant :
    echo    - index.php
    echo    - accueil.php  
    echo    - config/database.php
    echo    - SETUP_CLIENT/
    echo.
    pause
    exit /b 1
)

echo ✅ Dossier du projet détecté
echo.

set /p confirm="Créer le package ici ? (O/N) : "
if /i not "%confirm%"=="O" if /i not "%confirm%"=="Y" (
    echo ❌ Opération annulée
    pause
    exit /b
)

echo.
echo 🚀 Création du package dans le dossier courant...
echo.

:: Supprimer l'ancien package s'il existe
if exist "CYBERPARC_CLIENT_PACKAGE" (
    echo 🗑️ Suppression de l'ancien package...
    rmdir /s /q "CYBERPARC_CLIENT_PACKAGE"
)

:: Créer la structure
echo 📁 Création de la structure...
mkdir "CYBERPARC_CLIENT_PACKAGE"
mkdir "CYBERPARC_CLIENT_PACKAGE\config"
mkdir "CYBERPARC_CLIENT_PACKAGE\uploads"
mkdir "CYBERPARC_CLIENT_PACKAGE\uploads\logos"
mkdir "CYBERPARC_CLIENT_PACKAGE\uploads\recus"
mkdir "CYBERPARC_CLIENT_PACKAGE\vendor"
mkdir "CYBERPARC_CLIENT_PACKAGE\DOCUMENTATION"

:: Copier les fichiers
echo 📄 Copie des fichiers PHP...
copy "*.php" "CYBERPARC_CLIENT_PACKAGE\" >nul 2>&1
copy "*.ico" "CYBERPARC_CLIENT_PACKAGE\" >nul 2>&1
copy "*.jpg" "CYBERPARC_CLIENT_PACKAGE\" >nul 2>&1
copy "composer.json" "CYBERPARC_CLIENT_PACKAGE\" >nul 2>&1
copy "composer.lock" "CYBERPARC_CLIENT_PACKAGE\" >nul 2>&1

echo 🗄️ Copie de la base de données...
copy "SETUP_CLIENT\stage.sql" "CYBERPARC_CLIENT_PACKAGE\" >nul

echo ⚙️ Copie de la configuration...
copy "SETUP_CLIENT\database.php" "CYBERPARC_CLIENT_PACKAGE\config\" >nul

echo 📚 Copie de la documentation...
copy "SETUP_CLIENT\README.md" "CYBERPARC_CLIENT_PACKAGE\" >nul
copy "SETUP_CLIENT\GUIDE_RAPIDE.html" "CYBERPARC_CLIENT_PACKAGE\" >nul
copy "SETUP_CLIENT\CHECKLIST.md" "CYBERPARC_CLIENT_PACKAGE\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\DOCUMENTATION_COMPLETE.md" "CYBERPARC_CLIENT_PACKAGE\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\RACCOURCIS_ADMIN.html" "CYBERPARC_CLIENT_PACKAGE\DOCUMENTATION\" >nul

echo 🛠️ Copie des outils...
copy "SETUP_CLIENT\verifier_installation.php" "CYBERPARC_CLIENT_PACKAGE\" >nul
copy "SETUP_CLIENT\INSTALLATION_RAPIDE.bat" "CYBERPARC_CLIENT_PACKAGE\" >nul

:: Copier vendor si disponible
if exist "vendor" (
    echo 📦 Copie des dépendances...
    xcopy "vendor" "CYBERPARC_CLIENT_PACKAGE\vendor" /e /i /q >nul 2>&1
    echo ✅ Dépendances copiées
) else (
    echo ⚠️ Dossier vendor non trouvé
)

:: Créer les fichiers supplémentaires
echo 📝 Création des fichiers d'aide...

:: LISEZ-MOI.md
(
echo # 🚀 CyberParc - Système de Gestion Comptable
echo.
echo ## 🚀 Installation Rapide
echo.
echo ### Étape 1 : Base de données
echo 1. Ouvrez phpMyAdmin
echo 2. Créez une base `stage`
echo 3. Importez `stage.sql`
echo.
echo ### Étape 2 : Configuration  
echo 1. Modifiez `config/database.php`
echo 2. Changez les paramètres MySQL
echo.
echo ### Étape 3 : Test
echo 1. Accédez à votre site
echo 2. Connexion : <EMAIL> / admin123
echo 3. **CHANGEZ LE MOT DE PASSE !**
echo.
echo ## 📞 Support
echo - GUIDE_RAPIDE.html ^(guide visuel^)
echo - verifier_installation.php ^(diagnostic^)
echo.
echo **Version** : CyberParc v1.0
) > "CYBERPARC_CLIENT_PACKAGE\LISEZ-MOI.md"

:: Script d'installation express
(
echo @echo off
echo chcp 65001 ^>nul
echo echo 🚀 CYBERPARC - Installation Express
echo echo.
echo echo 📋 ÉTAPES :
echo echo 1️⃣  Importez stage.sql dans phpMyAdmin
echo echo 2️⃣  Modifiez config\database.php
echo echo 3️⃣  Accédez à votre site
echo echo 4️⃣  Connexion : <EMAIL> / admin123
echo echo 5️⃣  CHANGEZ LE MOT DE PASSE !
echo echo.
echo pause
) > "CYBERPARC_CLIENT_PACKAGE\INSTALLATION_EXPRESS.bat"

echo.
echo ✅ Package créé avec succès !
echo.
echo 📍 Emplacement : %CD%\CYBERPARC_CLIENT_PACKAGE
echo.
echo 🚀 Ouverture du dossier...
start "" "%CD%\CYBERPARC_CLIENT_PACKAGE"

echo.
echo 📦 Contenu créé :
dir "CYBERPARC_CLIENT_PACKAGE" /b

echo.
echo 💡 Pour envoyer à votre client :
echo    1. Compressez le dossier CYBERPARC_CLIENT_PACKAGE en ZIP
echo    2. Envoyez l'archive par email
echo    3. Incluez les instructions d'installation
echo.

echo 🎯 Package prêt pour envoi client !
pause
