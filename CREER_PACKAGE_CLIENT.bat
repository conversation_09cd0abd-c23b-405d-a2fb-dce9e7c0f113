@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                📦 CRÉATION PACKAGE CLIENT 📦                ║
echo ║                   CyberParcApp - Version 1.0                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Création du package CyberParcApp pour vos clients...
echo.

:: C<PERSON><PERSON> le dossier sur le bureau
set "DESKTOP=%USERPROFILE%\Desktop"
set "PACKAGE_DIR=%DESKTOP%\CYBERPARC_CLIENT_PACKAGE"

if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

echo 📍 Création du package sur le bureau : %PACKAGE_DIR%

echo 📁 Création de la structure des dossiers...
mkdir "%PACKAGE_DIR%\config"
mkdir "%PACKAGE_DIR%\uploads"
mkdir "%PACKAGE_DIR%\uploads\logos"
mkdir "%PACKAGE_DIR%\uploads\recus"
mkdir "%PACKAGE_DIR%\vendor"
mkdir "%PACKAGE_DIR%\DOCUMENTATION"

echo 📄 Copie des fichiers PHP principaux...
copy "*.php" "%PACKAGE_DIR%\" >nul 2>&1
copy "*.ico" "%PACKAGE_DIR%\" >nul 2>&1
copy "*.jpg" "%PACKAGE_DIR%\" >nul 2>&1
copy "composer.json" "%PACKAGE_DIR%\" >nul 2>&1
copy "composer.lock" "%PACKAGE_DIR%\" >nul 2>&1

echo 🗄️ Copie de la base de données...
copy "SETUP_CLIENT\stage.sql" "%PACKAGE_DIR%\" >nul

echo ⚙️ Copie de la configuration...
copy "SETUP_CLIENT\database.php" "%PACKAGE_DIR%\config\" >nul

echo 📚 Copie de la documentation...
copy "SETUP_CLIENT\README.md" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\GUIDE_RAPIDE.html" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\CHECKLIST.md" "%PACKAGE_DIR%\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\DOCUMENTATION_COMPLETE.md" "%PACKAGE_DIR%\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\RACCOURCIS_ADMIN.html" "%PACKAGE_DIR%\DOCUMENTATION\" >nul

echo 🛠️ Copie des outils d'installation...
copy "SETUP_CLIENT\verifier_installation.php" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\INSTALLATION_RAPIDE.bat" "%PACKAGE_DIR%\" >nul

echo 📦 Copie des dépendances Composer...
if exist "vendor" (
    xcopy "vendor" "%PACKAGE_DIR%\vendor" /e /i /q >nul 2>&1
    echo ✅ Dépendances Composer copiées
) else (
    echo ⚠️ Dossier vendor non trouvé - Le client devra exécuter 'composer install'
)

echo.
echo 📝 Création du fichier LISEZ-MOI...
(
echo # 🚀 CyberParcApp - Système de Gestion Comptable
echo.
echo ## 📋 Contenu du package
echo.
echo ### Fichiers principaux :
echo - **index.php** : Page de connexion
echo - **accueil.php** : Tableau de bord principal
echo - **stage.sql** : Base de données à importer
echo - **config/database.php** : Configuration à modifier
echo.
echo ### Documentation :
echo - **README.md** : Guide d'installation rapide
echo - **GUIDE_RAPIDE.html** : Guide visuel ^(ouvrir dans un navigateur^)
echo - **DOCUMENTATION/** : Documentation complète
echo.
echo ### Outils :
echo - **INSTALLATION_RAPIDE.bat** : Installation automatique Windows
echo - **verifier_installation.php** : Vérification du système
echo.
echo ## 🚀 Installation Rapide
echo.
echo ### Étape 1 : Préparer la base de données
echo 1. Ouvrez phpMyAdmin
echo 2. Créez une base de données nommée `stage`
echo 3. Importez le fichier `stage.sql`
echo.
echo ### Étape 2 : Configuration
echo 1. Modifiez le fichier `config/database.php`
echo 2. Changez les paramètres de connexion MySQL
echo 3. Configurez l'email ^(optionnel^)
echo.
echo ### Étape 3 : Test
echo 1. Accédez à votre site
echo 2. Connectez-vous avec : **<EMAIL>** / **admin123**
echo 3. **CHANGEZ IMMÉDIATEMENT LE MOT DE PASSE !**
echo.
echo ## 📞 Support
echo.
echo - Consultez **GUIDE_RAPIDE.html** pour un guide visuel
echo - Utilisez **verifier_installation.php** pour diagnostiquer les problèmes
echo - Référez-vous à la **DOCUMENTATION/** pour les détails techniques
echo.
echo ## 🔒 Sécurité
echo.
echo ⚠️ **IMPORTANT** : Changez le mot de passe administrateur dès la première connexion !
echo.
echo ---
echo **Version** : CyberParcApp v1.0
echo **Date** : Janvier 2025  
echo **Support** : Consultez la documentation incluse
) > "%PACKAGE_DIR%\LISEZ-MOI.md"

echo.
echo 🎯 Création du fichier d'installation express...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo.
echo echo ╔══════════════════════════════════════════════════════════════╗
echo echo ║                    🚀 CYBERPARC SETUP 🚀                    ║
echo echo ║              Installation Express - 5 minutes               ║
echo echo ╚══════════════════════════════════════════════════════════════╝
echo echo.
echo echo 📋 ÉTAPES D'INSTALLATION :
echo echo.
echo echo 1️⃣  Importez 'stage.sql' dans phpMyAdmin
echo echo 2️⃣  Modifiez 'config\database.php' avec vos paramètres
echo echo 3️⃣  Accédez à votre site web
echo echo 4️⃣  Connectez-vous : <EMAIL> / admin123
echo echo 5️⃣  CHANGEZ LE MOT DE PASSE IMMÉDIATEMENT !
echo echo.
echo echo 🔧 OUTILS DISPONIBLES :
echo echo    - GUIDE_RAPIDE.html ^(guide visuel^)
echo echo    - verifier_installation.php ^(diagnostic^)
echo echo    - DOCUMENTATION\ ^(aide complète^)
echo echo.
echo echo 📞 En cas de problème, consultez README.md
echo echo.
echo pause
) > "%PACKAGE_DIR%\INSTALLATION_EXPRESS.bat"

echo.
echo 🔧 Création du fichier de vérification rapide...
(
echo ^<?php
echo // Vérification rapide - CyberParc
echo echo "^<h1^>🔍 Vérification Rapide CyberParc^</h1^>";
echo if ^(file_exists^('config/database.php'^)^) {
echo     echo "^<p style='color:green'^>✅ Configuration trouvée^</p^>";
echo     require_once 'config/database.php';
echo     try {
echo         $pdo = new PDO^($dsn, $username, $password, $options^);
echo         echo "^<p style='color:green'^>✅ Connexion base de données OK^</p^>";
echo         echo "^<p^>^<a href='index.php'^>👉 Accéder au système^</a^>^</p^>";
echo     } catch ^(Exception $e^) {
echo         echo "^<p style='color:red'^>❌ Erreur BDD: " . $e-^>getMessage^(^) . "^</p^>";
echo     }
echo } else {
echo     echo "^<p style='color:red'^>❌ Configuration manquante^</p^>";
echo }
echo ?^>
) > "%PACKAGE_DIR%\test_rapide.php"

echo.
echo ✅ Package créé avec succès !
echo.
echo 📊 CONTENU DU PACKAGE :
echo    📁 Tous les fichiers PHP du système
echo    📁 Base de données ^(stage.sql^)
echo    📁 Configuration template
echo    📁 Documentation complète
echo    📁 Outils d'installation
echo    📁 Dépendances ^(si disponibles^)
echo.

:: Créer le ZIP si 7-Zip est disponible
where 7z >nul 2>&1
if %errorlevel% == 0 (
    echo 🗜️ Création de l'archive ZIP sur le bureau...
    7z a -tzip "%DESKTOP%\CyberParc_Client_Package.zip" "%PACKAGE_DIR%\*" >nul
    echo ✅ Archive créée : %DESKTOP%\CyberParc_Client_Package.zip
    echo.
) else (
    echo ℹ️ 7-Zip non trouvé - Archive manuelle nécessaire
    echo    Compressez le dossier manuellement depuis le bureau
    echo.
)

echo 🎯 PRÊT À ENVOYER !
echo.
echo 📦 Dossier : %PACKAGE_DIR%
echo 📧 Archive : %DESKTOP%\CyberParc_Client_Package.zip ^(si créée^)
echo.
echo 💡 INSTRUCTIONS POUR LE CLIENT :
echo    1. Décompresser l'archive
echo    2. Lire LISEZ-MOI.md
echo    3. Exécuter INSTALLATION_EXPRESS.bat
echo    4. Suivre les étapes affichées
echo.

pause
echo.
echo 🚀 Package prêt pour envoi client !
echo 📍 Emplacement : Bureau de votre ordinateur
echo.
echo 💡 Vous pouvez maintenant :
echo    1. Ouvrir le dossier sur votre bureau
echo    2. Envoyer l'archive ZIP à votre client
echo    3. Ou compresser manuellement le dossier
echo.
start "" "%DESKTOP%"
pause
