<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guide d'Installation Rapide - CyberParc</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .step {
            background: #ecf0f1;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
            border-radius: 5px;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        code {
            background: #f8f9fa;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Guide d'Installation Rapide - CyberParc</h1>
        
        <div class="warning">
            <strong>⚠️ Important :</strong> Ce guide est destiné aux non-développeurs. Suivez chaque étape dans l'ordre.
        </div>

        <h2>📋 Ce dont vous avez besoin</h2>
        <ul>
            <li>Un hébergement web avec PHP 7.4+ et MySQL</li>
            <li>Accès à phpMyAdmin ou équivalent</li>
            <li>Les fichiers de CyberParc (déjà téléchargés)</li>
        </ul>

        <h2>🔧 Installation en 5 étapes</h2>

        <div class="step">
            <h3>Étape 1 : Préparer la base de données</h3>
            <ol>
                <li>Connectez-vous à phpMyAdmin</li>
                <li>Cliquez sur "Nouvelle base de données"</li>
                <li>Nommez-la <code>stage</code></li>
                <li>Cliquez sur "Créer"</li>
                <li>Sélectionnez votre base de données</li>
                <li>Cliquez sur "Importer"</li>
                <li>Choisissez le fichier <code>stage.sql</code></li>
                <li>Cliquez sur "Exécuter"</li>
            </ol>
        </div>

        <div class="step">
            <h3>Étape 2 : Télécharger les fichiers</h3>
            <ol>
                <li>Téléchargez tous les fichiers PHP sur votre serveur</li>
                <li>Placez-les dans le dossier racine de votre site</li>
                <li>Assurez-vous que le fichier <code>index.php</code> est à la racine</li>
            </ol>
        </div>

        <div class="step">
            <h3>Étape 3 : Configuration</h3>
            <ol>
                <li>Ouvrez le fichier <code>config/database.php</code></li>
                <li>Modifiez ces lignes selon vos paramètres :</li>
            </ol>
            <div class="code-block">
$host = 'localhost';           // Votre serveur MySQL
$dbname = 'stage';            // Nom de votre base
$username = 'votre_user';     // Votre utilisateur MySQL
$password = 'votre_mdp';      // Votre mot de passe MySQL
            </div>
        </div>

        <div class="step">
            <h3>Étape 4 : Permissions des dossiers</h3>
            <p>Créez ces dossiers et donnez-leur les permissions d'écriture :</p>
            <ul>
                <li><code>uploads/</code></li>
                <li><code>uploads/logos/</code></li>
                <li><code>uploads/recus/</code></li>
            </ul>
        </div>

        <div class="step">
            <h3>Étape 5 : Premier test</h3>
            <ol>
                <li>Accédez à votre site : <code>http://votre-domaine.com</code></li>
                <li>Vous devriez voir la page de connexion</li>
                <li>Connectez-vous avec :</li>
                <ul>
                    <li><strong>Email :</strong> <EMAIL></li>
                    <li><strong>Mot de passe :</strong> admin123</li>
                </ul>
            </ol>
        </div>

        <div class="warning">
            <strong>🔒 Sécurité importante :</strong>
            <ul>
                <li>Changez immédiatement le mot de passe administrateur</li>
                <li>Créez vos propres comptes utilisateurs</li>
                <li>Supprimez le compte admin par défaut après avoir créé le vôtre</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Installation terminée !</h3>
            <p>Votre système CyberParc est maintenant opérationnel. Vous pouvez commencer à ajouter vos entreprises et gérer votre comptabilité.</p>
        </div>

        <h2>📞 Besoin d'aide ?</h2>
        <p>Si vous rencontrez des problèmes :</p>
        <ul>
            <li>Vérifiez que PHP et MySQL sont bien installés</li>
            <li>Consultez les logs d'erreur de votre serveur</li>
            <li>Assurez-vous que tous les fichiers ont été téléchargés</li>
            <li>Contactez votre hébergeur pour les questions techniques</li>
        </ul>
    </div>
</body>
</html>
