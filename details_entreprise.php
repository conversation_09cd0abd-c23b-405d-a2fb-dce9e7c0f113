<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Vérifier si le dossier classes existe, sinon le créer
if (!file_exists('classes')) {
  mkdir('classes', 0755, true);
}

// Inclure le gestionnaire de fichiers seulement s'il existe
$fileManagerAvailable = false;
if (file_exists('classes/FileUploadManager.php')) {
  require_once 'classes/FileUploadManager.php';
  $fileManagerAvailable = true;
}

try {
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  if (!isset($_GET['id']) || empty($_GET['id'])) {
    die("ID d'entreprise manquant.");
  }

  $id = intval($_GET['id']);
  $fileManager = $fileManagerAvailable ? new FileUploadManager() : null;

  // Récupérer les détails de l'entreprise
  $entreprise = $pdo->prepare("SELECT * FROM entreprises WHERE id = ?");
  $entreprise->execute([$id]);
  $e = $entreprise->fetch(PDO::FETCH_ASSOC);

  if (!$e) {
    die("Entreprise non trouvée.");
  }

  $active = isset($e['active']) && $e['active'] == 1;

  // Récupérer les factures
  $factures = $pdo->prepare("SELECT * FROM facture WHERE entreprise_id = ? ORDER BY dateDebut DESC");
  $factures->execute([$id]);

  // Calculer la somme totale des factures
  $totalFactures = $pdo->prepare("SELECT COALESCE(SUM(Montant), 0) as total FROM facture WHERE entreprise_id = ?");
  $totalFactures->execute([$id]);
  $sommeFactures = $totalFactures->fetch(PDO::FETCH_ASSOC)['total'];

  // Vérifier si la colonne date_creation existe dans la table virement
  $columns = $pdo->query("SHOW COLUMNS FROM virement")->fetchAll(PDO::FETCH_COLUMN);
  $hasDateCreation = in_array('date_creation', $columns);

  // Récupérer les virements avec tri approprié
  if ($hasDateCreation) {
    $virements = $pdo->prepare("SELECT * FROM virement WHERE entreprise_id = ? ORDER BY date_creation DESC");
  } else {
    $virements = $pdo->prepare("SELECT * FROM virement WHERE entreprise_id = ? ORDER BY id DESC");
  }
  $virements->execute([$id]);

  // Calculer la somme totale des virements
  $totalVirements = $pdo->prepare("SELECT COALESCE(SUM(Montant), 0) as total FROM virement WHERE entreprise_id = ?");
  $totalVirements->execute([$id]);
  $sommeVirements = $totalVirements->fetch(PDO::FETCH_ASSOC)['total'];

  // Calculer le solde restant (factures - virements)
  $soldeRestant = $sommeFactures - $sommeVirements;

  // Messages de succès
  $successMessage = '';
  if (isset($_GET['success'])) {
    switch ($_GET['success']) {
      case 'virement_ajoute':
        $successMessage = 'Virement ajouté avec succès !';
        break;
      case 'facture_ajoutee':
        $successMessage = 'Facture ajoutée avec succès !';
        break;
      case 'facture_modifiee':
        $successMessage = 'Facture modifiée avec succès !';
        break;
      case 'facture_supprimee':
        $successMessage = 'Facture supprimée avec succès !';
        break;
    }
  }

  // Fonction pour déterminer le trimestre à partir des dates
  function getTrimestreFromDates($dateDebut, $dateFin) {
    if (!$dateDebut || !$dateFin) return "-";
    
    $debut = new DateTime($dateDebut);
    $fin = new DateTime($dateFin);
    $annee = $debut->format('Y');
    
    $moisDebut = (int)$debut->format('n');
    $moisFin = (int)$fin->format('n');
    
    // Déterminer le trimestre basé sur les mois
    if ($moisDebut == 1 && $moisFin == 3) {
      return "T1 $annee";
    } elseif ($moisDebut == 4 && $moisFin == 6) {
      return "T2 $annee";
    } elseif ($moisDebut == 7 && $moisFin == 9) {
      return "T3 $annee";
    } elseif ($moisDebut == 10 && $moisFin == 12) {
      return "T4 $annee";
    } else {
      // Période personnalisée
      return $debut->format('d/m/Y') . ' au ' . $fin->format('d/m/Y');
    }
  }

  // Fonction pour extraire le trimestre et l'année des dates
  function getTrimestreData($dateDebut, $dateFin) {
    if (!$dateDebut || !$dateFin) return ['trimestre' => '', 'annee' => ''];
    
    $debut = new DateTime($dateDebut);
    $fin = new DateTime($dateFin);
    $annee = $debut->format('Y');
    
    $moisDebut = (int)$debut->format('n');
    $moisFin = (int)$fin->format('n');
    
    // Déterminer le trimestre basé sur les mois
    if ($moisDebut == 1 && $moisFin == 3) {
      return ['trimestre' => 'T1', 'annee' => $annee];
    } elseif ($moisDebut == 4 && $moisFin == 6) {
      return ['trimestre' => 'T2', 'annee' => $annee];
    } elseif ($moisDebut == 7 && $moisFin == 9) {
      return ['trimestre' => 'T3', 'annee' => $annee];
    } elseif ($moisDebut == 10 && $moisFin == 12) {
      return ['trimestre' => 'T4', 'annee' => $annee];
    } else {
      return ['trimestre' => '', 'annee' => $annee];
    }
  }

} catch (PDOException $e) {
  die("Erreur base de données : " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Détails | <?= htmlspecialchars($e['nom']) ?></title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, #1e3c72, #2a5298);
      color: #fff;
      min-height: 100vh;
      padding: 2rem;
    }

    .container {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 2.5rem;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    .header {
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 2rem;
      padding-bottom: 2rem;
      border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    }

    .logo-container {
      flex-shrink: 0;
    }

    .company-logo {
      width: 120px;
      height: 120px;
      border-radius: 15px;
      object-fit: cover;
      border: 3px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    }

    .no-logo {
      width: 120px;
      height: 120px;
      border-radius: 15px;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 3rem;
      border: 2px dashed rgba(255, 255, 255, 0.4);
    }

    .company-info h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: #fff;
    }

    .company-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }

    .detail-item {
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem;
      border-radius: 10px;
      backdrop-filter: blur(5px);
    }

    .detail-label {
      font-weight: 600;
      color: #e0e7ff;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }

    .detail-value {
      font-size: 1.1rem;
      color: #fff;
    }

    .status-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .status-active {
      background: #10b981;
      color: white;
    }

    .status-inactive {
      background: #ef4444;
      color: white;
    }

    .financial-summary {
      background: rgba(255, 255, 255, 0.15);
      border-radius: 20px;
      padding: 2rem;
      margin: 2rem 0;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .financial-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .financial-card {
      background: rgba(255, 255, 255, 0.1);
      padding: 1.5rem;
      border-radius: 15px;
      text-align: center;
      backdrop-filter: blur(5px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .financial-card.positive {
      background: rgba(16, 185, 129, 0.2);
      border-color: rgba(16, 185, 129, 0.3);
    }

    .financial-card.negative {
      background: rgba(239, 68, 68, 0.2);
      border-color: rgba(239, 68, 68, 0.3);
    }

    .financial-card.neutral {
      background: rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.3);
    }

    .financial-icon {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    .financial-label {
      font-size: 0.9rem;
      color: #e0e7ff;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }

    .financial-amount {
      font-size: 1.8rem;
      font-weight: 700;
      color: #fff;
    }

    .balance-highlight {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
      border: 2px solid rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    .section {
      margin: 3rem 0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
    }

    .section-title {
      font-size: 1.8rem;
      font-weight: 700;
      color: #fff;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .table-container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    th {
      background: #1e90ff;
      color: white;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    td {
      color: #374151;
      font-size: 0.95rem;
    }

    tbody tr:hover {
      background: #f8fafc;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background: linear-gradient(135deg, #1e90ff, #1e3c72);
      color: white;
      text-decoration: none;
      border: none;
      border-radius: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.9rem;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(30, 144, 255, 0.4);
    }

    .btn-small {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .btn-success {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    .btn-danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .btn-warning {
      background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .action-buttons {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .form-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(5px);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .form-modal {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      max-width: 500px;
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .form-modal h3 {
      color: #1e3c72;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #374151;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #1e90ff;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .file-upload-area {
      border: 2px dashed #d1d5db;
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      background: #f9fafb;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #374151;
    }

    .file-upload-area:hover {
      border-color: #1e90ff;
      background: #eff6ff;
    }

    .file-preview {
      margin-top: 1rem;
      padding: 1rem;
      background: #f3f4f6;
      border-radius: 8px;
      display: none;
    }

    .receipt-thumbnail {
      max-width: 100px;
      max-height: 100px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .receipt-thumbnail:hover {
      transform: scale(1.1);
    }

    .file-link {
      color: #1e90ff;
      text-decoration: none;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .file-link:hover {
      text-decoration: underline;
    }

    .alert {
      padding: 1rem;
      border-radius: 12px;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
    }

    .alert-success {
      background: #f0fdf4;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .warning-message {
      background: #fef3c7;
      color: #92400e;
      border: 1px solid #fed7aa;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1rem;
    }

    .enhanced-header {
      display: flex;
      gap: 2rem;
      padding: 2rem;
      background: rgba(255, 255, 255, 0.07);
      border-radius: 20px;
      align-items: center;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .company-left {
      flex-shrink: 0;
    }

    .company-logo-large {
      width: 140px;
      height: 140px;
      object-fit: cover;
      border-radius: 16px;
      border: 3px solid rgba(255, 255, 255, 0.4);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .no-logo-large {
      width: 140px;
      height: 140px;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16px;
      font-size: 3rem;
      border: 2px dashed rgba(255, 255, 255, 0.4);
    }

    .company-right {
      flex: 1;
      color: #f1f5f9;
    }

    .company-name {
      font-size: 2.2rem;
      font-weight: 700;
      color: #ffffff;
    }

    .company-description {
      color: #dbeafe;
      margin-bottom: 1rem;
    }

    .company-detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1rem;
      font-size: 1rem;
    }

    .company-detail-grid .item {
      background: rgba(255, 255, 255, 0.1);
      padding: 0.75rem 1rem;
      border-radius: 12px;
      backdrop-filter: blur(4px);
    }

    .company-detail-grid .item a {
      color: #60a5fa;
      text-decoration: none;
    }

    .company-detail-grid .item a:hover {
      text-decoration: underline;
    }

    #uploadText {
      color: #374151;
      font-weight: 500;
    }

    .highlight-row {
      animation: highlight 2s ease-out;
      background-color: #ffeb3b !important;
      color: #333 !important;
    }

    @keyframes highlight {
      0% {
        background-color: #ffeb3b;
      }

      100% {
        background-color: transparent;
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 1.5rem;
      }

      .header {
        flex-direction: column;
        text-align: center;
      }

      .company-details {
        grid-template-columns: 1fr;
      }

      .financial-grid {
        grid-template-columns: 1fr;
      }

      .section-header {
        flex-direction: column;
        gap: 1rem;
      }

      table {
        font-size: 0.8rem;
      }

      th,
      td {
        padding: 0.5rem;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .action-buttons {
        flex-direction: column;
      }

      .company-details {
        padding: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.05);
        margin-top: 1rem;
        animation: fadeIn 0.8s ease-in-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }

        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- Messages de succès -->
    <?php if ($successMessage): ?>
      <div class="alert alert-success">
        <span>✅</span>
        <?= htmlspecialchars($successMessage) ?>
      </div>
    <?php endif; ?>

    <!-- Avertissement si FileUploadManager n'est pas disponible -->
    <?php if (!$fileManagerAvailable): ?>
      <div class="warning-message">
        ⚠ <strong>Attention :</strong> Le gestionnaire de fichiers n'est pas disponible. Les fonctionnalités d'upload sont limitées.
      </div>
    <?php endif; ?>

    <!-- En-tête avec logo et informations -->
    <div class="header enhanced-header">
      <div class="company-left">
        <?php if (!empty($e['logo']) && $fileManager): ?>
          <?php $logoUrl = $fileManager->getFileUrl($e['logo'], 'logo'); ?>
          <?php if ($logoUrl): ?>
            <img src="<?= htmlspecialchars($logoUrl) ?>" alt="Logo <?= htmlspecialchars($e['nom']) ?>" class="company-logo-large">
          <?php else: ?>
            <div class="no-logo-large">🏢</div>
          <?php endif; ?>
        <?php else: ?>
          <div class="no-logo-large">🏢</div>
        <?php endif; ?>
      </div>
      <div class="company-right">
        <h1 class="company-name"><?= htmlspecialchars($e['nom']) ?></h1>
        <p class="company-description">📁 Fiche de l'entreprise enregistrée</p>
        <div class="company-detail-grid">
          <div class="item">
            <strong>👤 Chef :</strong> <?= htmlspecialchars($e['chef']) ?>
          </div>
          <div class="item">
            <strong>📧 Email :</strong>
            <a href="mailto:<?= htmlspecialchars($e['email']) ?>"><?= htmlspecialchars($e['email']) ?></a>
          </div>
          <div class="item">
            <strong>📞 Téléphone :</strong> <?= htmlspecialchars($e['numero']) ?>
          </div>
          <div class="item">
            <strong>📍 Local :</strong> <?= htmlspecialchars($e['local']) ?>
          </div>
          <div class="item">
            <strong>📊 Statut :</strong>
            <?php if ($active): ?>
              <span class="status-badge status-active">✅ Active</span>
            <?php else: ?>
              <span class="status-badge status-inactive">❌ Inactive</span>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>

    <!-- Résumé financier -->
    <div class="financial-summary">
      <h2 style="color: #fff; margin-bottom: 1.5rem; text-align: center; font-size: 1.8rem;">
        💰 Résumé Financier
      </h2>
      <div class="financial-grid">
        <div class="financial-card neutral">
          <div class="financial-icon">📄</div>
          <div class="financial-label">Total Factures</div>
          <div class="financial-amount"><?= number_format($sommeFactures, 2, ',', ' ') ?> DT</div>
        </div>
        <div class="financial-card positive">
          <div class="financial-icon">💳</div>
          <div class="financial-label">Total Virements</div>
          <div class="financial-amount"><?= number_format($sommeVirements, 2, ',', ' ') ?> DT</div>
        </div>
        <div class="financial-card <?= $soldeRestant > 0 ? 'negative' : ($soldeRestant < 0 ? 'positive' : 'neutral') ?> balance-highlight">
          <div class="financial-icon"><?= $soldeRestant > 0 ? '⚠' : ($soldeRestant < 0 ? '✅' : '⚖') ?></div>
          <div class="financial-label">
            <?= $soldeRestant > 0 ? 'Solde Restant Dû' : ($soldeRestant < 0 ? 'Excédent Payé' : 'Solde Équilibré') ?>
          </div>
          <div class="financial-amount"><?= number_format(abs($soldeRestant), 2, ',', ' ') ?> DT</div>
        </div>
      </div>
      <?php if ($soldeRestant > 0): ?>
        <div style="text-align: center; color: #fbbf24; font-weight: 600; margin-top: 1rem;">
          ⚠ Il reste <?= number_format($soldeRestant, 2, ',', ' ') ?> DT à recevoir
        </div>
      <?php elseif ($soldeRestant < 0): ?>
        <div style="text-align: center; color: #10b981; font-weight: 600; margin-top: 1rem;">
          ✅ Excédent de <?= number_format(abs($soldeRestant), 2, ',', ' ') ?> DT payé
        </div>
      <?php else: ?>
        <div style="text-align: center; color: #60a5fa; font-weight: 600; margin-top: 1rem;">
          ⚖ Comptes équilibrés - Aucun solde restant
        </div>
      <?php endif; ?>
    </div>

    <!-- Section Factures -->
    <div class="section">
      <div class="section-header">
        <h2 class="section-title">📄 Factures (Total: <?= number_format($sommeFactures, 2, ',', ' ') ?> DT)</h2>
        <?php if ($active): ?>
          <button class="btn" onclick="openModal('factureForm')">
            ➕ Ajouter une facture
          </button>
        <?php endif; ?>
      </div>
      <div class="table-container">
        <?php $facturesData = $factures->fetchAll(); ?>
        <?php if (empty($facturesData)): ?>
          <div class="empty-state">
            <div class="empty-state-icon">📄</div>
            <h3>Aucune facture</h3>
            <p>Cette entreprise n'a pas encore de factures enregistrées.</p>
          </div>
        <?php else: ?>
          <table>
            <thead>
              <tr>
                <th>#</th>
                <th>Numéro</th>
                <th>Trimestre</th>
                <th>Montant</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($facturesData as $fact): ?>
                <?php
                $debut = isset($fact['dateDebut']) ? strtotime($fact['dateDebut']) : null;
                $fin = isset($fact['dateFin']) ? strtotime($fact['dateFin']) : null;
                $now = time();
                $periodeExpiree = ($fin !== null && $fin < $now);
                ?>
                <tr id="facture-row-<?= $fact['id'] ?>" <?= $periodeExpiree ? 'style="background-color: #f8d7da;"' : '' ?>>
                  <td><?= $fact['id'] ?></td>
                  <td><?= htmlspecialchars($fact['numFact'] ?? '') ?></td>
                  <td>
                    <?php
                    $trimestre = getTrimestreFromDates($fact['dateDebut'], $fact['dateFin']);
                    echo htmlspecialchars($trimestre);
                    if ($periodeExpiree) {
                      echo ' <span style="color: #b91c1c; font-weight: bold;">(Période terminée)</span>';
                    }
                    ?>
                  </td>
                  <td><strong><?= number_format($fact['Montant'], 2, ',', ' ') ?> DT</strong></td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn btn-small btn-warning" onclick="editFacture(<?= $fact['id'] ?>, '<?= htmlspecialchars($fact['numFact']) ?>', '<?= htmlspecialchars($fact['Montant']) ?>', '<?= htmlspecialchars($fact['dateDebut']) ?>', '<?= htmlspecialchars($fact['dateFin']) ?>')">
                        ✏️ Modifier
                      </button>
                      <button class="btn btn-small btn-danger" onclick="deleteFacture(<?= $fact['id'] ?>, '<?= htmlspecialchars($fact['numFact']) ?>')">
                        🗑️ Supprimer
                      </button>
                    </div>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        <?php endif; ?>
      </div>
    </div>

    <!-- Section Virements -->
    <div class="section">
      <div class="section-header">
        <h2 class="section-title">💰 Virements (Total: <?= number_format($sommeVirements, 2, ',', ' ') ?> DT)</h2>
        <?php if ($active): ?>
          <button class="btn btn-success" onclick="openModal('virementForm')">
            ➕ Ajouter un virement
          </button>
        <?php endif; ?>
      </div>
      <div class="table-container">
        <?php $virementsData = $virements->fetchAll(); ?>
        <?php if (empty($virementsData)): ?>
          <div class="empty-state">
            <div class="empty-state-icon">💰</div>
            <h3>Aucun virement</h3>
            <p>Cette entreprise n'a pas encore de virements enregistrés.</p>
          </div>
        <?php else: ?>
          <table>
            <thead>
              <tr>
                <th>#</th>
                <th>Numéro</th>
                <th>Type de paiement</th>
                <th>Montant</th>
                <th>Reçu</th>
                <?php if ($hasDateCreation): ?>
                  <th>Date</th>
                <?php endif; ?>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($virementsData as $vir): ?>
                <tr>
                  <td><?= $vir['id'] ?></td>
                  <td><?= htmlspecialchars($vir['numVirem']) ?></td>
                  <td><?= htmlspecialchars($vir['typePai']) ?></td>
                  <td><strong><?= number_format($vir['Montant'], 2, ',', ' ') ?> DT</strong></td>
                  <td>
                    <?php if (!empty($vir['fichierRecu'])): ?>
                      <?php if ($fileManager): ?>
                        <?php $recuUrl = $fileManager->getFileUrl($vir['fichierRecu'], 'recu'); ?>
                        <?php if ($recuUrl): ?>
                          <?php $fileInfo = $fileManager->getFileInfo($vir['fichierRecu'], 'recu'); ?>
                          <?php $extension = strtolower($fileInfo['extension'] ?? ''); ?>
                          <?php if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])): ?>
                            <a href="<?= htmlspecialchars($recuUrl) ?>" target="_blank" class="file-link">
                              <img src="<?= htmlspecialchars($recuUrl) ?>" alt="Reçu" class="receipt-thumbnail">
                              📷 Voir l'image
                            </a>
                          <?php else: ?>
                            <a href="<?= htmlspecialchars($recuUrl) ?>" target="_blank" class="file-link">
                              📄 Voir le document
                            </a>
                          <?php endif; ?>
                          <?php if ($fileInfo): ?>
                            <br><small><?= $fileInfo['size_formatted'] ?></small>
                          <?php endif; ?>
                        <?php else: ?>
                          <span style="color: #ef4444;">❌ Fichier introuvable</span>
                        <?php endif; ?>
                      <?php else: ?>
                        <!-- Fallback simple sans FileUploadManager -->
                        <?php $simplePath = 'uploads/recus/' . $vir['fichierRecu']; ?>
                        <?php if (file_exists($simplePath)): ?>
                          <a href="<?= htmlspecialchars($simplePath) ?>" target="_blank" class="file-link">
                            📄 Voir le fichier
                          </a>
                        <?php else: ?>
                          <span style="color: #ef4444;">❌ Fichier introuvable</span>
                        <?php endif; ?>
                      <?php endif; ?>
                    <?php else: ?>
                      <span style="color: #6b7280;">Aucun reçu</span>
                    <?php endif; ?>
                  </td>
                  <?php if ($hasDateCreation): ?>
                    <td>
                      <?php if (isset($vir['date_creation'])): ?>
                        <?= date('d/m/Y H:i', strtotime($vir['date_creation'])) ?>
                      <?php else: ?>
                        -
                      <?php endif; ?>
                    </td>
                  <?php endif; ?>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        <?php endif; ?>
      </div>
    </div>

    <!-- Bouton retour -->
    <div style="text-align: center; margin-top: 3rem;">
      <a href="accueil.php" class="btn btn-secondary">
        ← Retour à l'accueil
      </a>
    </div>
  </div>

  <!-- Formulaire Facture (Ajout) -->
  <div id="factureForm" class="form-overlay">
    <div class="form-modal">
      <h3>📄 Nouvelle facture</h3>
      <form action="ajouter_facture.php" method="post">
        <input type="hidden" name="entreprise_id" value="<?= $e['id'] ?>">
        <div class="form-group">
          <label for="numFact">Numéro de facture *</label>
          <input type="number" id="numFact" name="numFact" required>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="trimestre">Trimestre *</label>
            <select id="trimestre" name="trimestre" required>
              <option value="">Sélectionnez un trimestre</option>
              <option value="T1">T1 (Janvier - Mars)</option>
              <option value="T2">T2 (Avril - Juin)</option>
              <option value="T3">T3 (Juillet - Septembre)</option>
              <option value="T4">T4 (Octobre - Décembre)</option>
            </select>
          </div>
          <div class="form-group">
            <label for="annee">Année *</label>
            <select id="annee" name="annee" required>
              <option value="">Sélectionnez une année</option>
              <?php
              $currentYear = date('Y');
              // Générer toutes les années depuis 1920 jusqu'à l'année courante + 5
              for ($year = $currentYear + 5; $year >= 1920; $year--) {
                $selected = ($year == $currentYear) ? 'selected' : '';
                echo "<option value=\"$year\" $selected>$year</option>";
              }
              ?>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label for="MontantFact">Montant (DT) *</label>
          <input type="number" id="MontantFact" name="Montant" step="0.01" required>
        </div>
        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
          <button type="submit" class="btn" style="flex: 1;">
            ✅ Ajouter la facture
          </button>
          <button type="button" class="btn btn-secondary" onclick="closeModal('factureForm')" style="flex: 1;">
            ❌ Annuler
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Formulaire Facture (Modification) -->
  <div id="editFactureForm" class="form-overlay">
    <div class="form-modal">
      <h3>✏️ Modifier la facture</h3>
      <form action="modifier_facture.php" method="post">
        <input type="hidden" name="entreprise_id" value="<?= $e['id'] ?>">
        <input type="hidden" id="editFactureId" name="facture_id">
        <div class="form-group">
          <label for="editNumFact">Numéro de facture *</label>
          <input type="number" id="editNumFact" name="numFact" required>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="editTrimestre">Trimestre *</label>
            <select id="editTrimestre" name="trimestre" required>
              <option value="">Sélectionnez un trimestre</option>
              <option value="T1">T1 (Janvier - Mars)</option>
              <option value="T2">T2 (Avril - Juin)</option>
              <option value="T3">T3 (Juillet - Septembre)</option>
              <option value="T4">T4 (Octobre - Décembre)</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editAnnee">Année *</label>
            <select id="editAnnee" name="annee" required>
              <option value="">Sélectionnez une année</option>
              <?php
              for ($year = $currentYear + 5; $year >= 1920; $year--) {
                echo "<option value=\"$year\">$year</option>";
              }
              ?>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label for="editMontantFact">Montant (DT) *</label>
          <input type="number" id="editMontantFact" name="Montant" step="0.01" required>
        </div>
        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
          <button type="submit" class="btn btn-warning" style="flex: 1;">
            ✅ Modifier la facture
          </button>
          <button type="button" class="btn btn-secondary" onclick="closeModal('editFactureForm')" style="flex: 1;">
            ❌ Annuler
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Formulaire Virement -->
  <div id="virementForm" class="form-overlay">
    <div class="form-modal">
      <h3>💰 Nouveau virement</h3>
      <form action="ajouter_virement.php" method="post" enctype="multipart/form-data">
        <input type="hidden" name="entreprise_id" value="<?= $e['id'] ?>">
        <div class="form-group">
          <label for="numVirem">Numéro de virement *</label>
          <input type="number" id="numVirem" name="numVirem" required>
        </div>
        <div class="form-group">
          <label for="typePai">Type de paiement *</label>
          <select id="typePai" name="typePai" required>
            <option value="">Sélectionnez un type</option>
            <option value="Virement bancaire">Virement bancaire</option>
            <option value="Chèque">Chèque</option>
            <option value="Espèces">Espèces</option>
            <option value="Carte bancaire">Carte bancaire</option>
            <option value="Mobile Money">Mobile Money</option>
          </select>
        </div>
        <div class="form-group">
          <label for="MontantVir">Montant (DT) *</label>
          <input type="number" id="MontantVir" name="Montant" step="0.01" required>
        </div>
        <div class="form-group">
          <label for="recu">Reçu / Pièce jointe</label>
          <div class="file-upload-area" onclick="document.getElementById('recu').click()">
            <input type="file" id="recu" name="recu" accept="image/*,.pdf,.doc,.docx" style="display: none;" onchange="previewFile(this)">
            <div id="uploadText">
              📎 Cliquez pour sélectionner un fichier<br>
              <small>Images, PDF, Word - Max 5MB</small>
            </div>
          </div>
          <div id="filePreview" class="file-preview"></div>
        </div>
        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
          <button type="submit" class="btn btn-success" style="flex: 1;">
            ✅ Ajouter le virement
          </button>
          <button type="button" class="btn btn-secondary" onclick="closeModal('virementForm')" style="flex: 1;">
            ❌ Annuler
          </button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // Fonctions pour ouvrir et fermer les modals
    function openModal(modalId) {
      console.log('Ouverture du modal:', modalId);
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        console.log('Modal ouvert avec succès');
      } else {
        console.error('Modal non trouvé:', modalId);
      }
    }

    function closeModal(modalId) {
      console.log('Fermeture du modal:', modalId);
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';

        // Reset du formulaire
        const form = modal.querySelector('form');
        if (form) {
          form.reset();
        }

        // Reset de la prévisualisation de fichier
        resetFilePreview();
        console.log('Modal fermé avec succès');
      }
    }

    // Fonction pour modifier une facture
    function editFacture(id, numFact, montant, dateDebut, dateFin) {
      // Extraire le trimestre et l'année des dates
      const debut = new Date(dateDebut);
      const fin = new Date(dateFin);
      const annee = debut.getFullYear();
      
      let trimestre = '';
      const moisDebut = debut.getMonth() + 1;
      const moisFin = fin.getMonth() + 1;
      
      if (moisDebut === 1 && moisFin === 3) {
        trimestre = 'T1';
      } else if (moisDebut === 4 && moisFin === 6) {
        trimestre = 'T2';
      } else if (moisDebut === 7 && moisFin === 9) {
        trimestre = 'T3';
      } else if (moisDebut === 10 && moisFin === 12) {
        trimestre = 'T4';
      }

      // Remplir le formulaire de modification
      document.getElementById('editFactureId').value = id;
      document.getElementById('editNumFact').value = numFact;
      document.getElementById('editMontantFact').value = montant;
      document.getElementById('editTrimestre').value = trimestre;
      document.getElementById('editAnnee').value = annee;

      // Ouvrir le modal de modification
      openModal('editFactureForm');
    }

    // Fonction pour supprimer une facture
    function deleteFacture(id, numFact) {
      if (confirm(`Êtes-vous sûr de vouloir supprimer la facture n°${numFact} ?\n\nCette action est irréversible.`)) {
        // Créer un formulaire temporaire pour la suppression
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'supprimer_facture.php';
        
        const factureIdInput = document.createElement('input');
        factureIdInput.type = 'hidden';
        factureIdInput.name = 'facture_id';
        factureIdInput.value = id;
        
        const entrepriseIdInput = document.createElement('input');
        entrepriseIdInput.type = 'hidden';
        entrepriseIdInput.name = 'entreprise_id';
        entrepriseIdInput.value = '<?= $e['id'] ?>';
        
        form.appendChild(factureIdInput);
        form.appendChild(entrepriseIdInput);
        document.body.appendChild(form);
        form.submit();
      }
    }

    function previewFile(input) {
      console.log('Prévisualisation du fichier');
      const preview = document.getElementById('filePreview');
      const uploadText = document.getElementById('uploadText');

      if (input.files && input.files[0]) {
        const file = input.files[0];

        // Vérifier la taille du fichier (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          alert("Le fichier dépasse la taille maximale de 5MB.");
          input.value = '';
          return;
        }

        const fileSize = formatFileSize(file.size);
        const fileName = file.name;

        preview.innerHTML = `
          <div style="display: flex; align-items: center; gap: 1rem;">
            <div style="flex: 1;">
              <strong>${fileName}</strong><br>
              <small>${fileSize}</small>
            </div>
            <button type="button" onclick="clearFile()" style="background: #ef4444; color: white; border: none; padding: 0.5rem; border-radius: 5px; cursor: pointer;">
              ❌
            </button>
          </div>
        `;

        preview.style.display = 'block';
        uploadText.innerHTML = '✅ Fichier sélectionné<br><small>Cliquez pour changer</small>';

        // Prévisualisation d'image
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = function(e) {
            preview.innerHTML += `<img src="${e.target.result}" style="max-width: 200px; max-height: 150px; border-radius: 8px; margin-top: 1rem;">`;
          };
          reader.readAsDataURL(file);
        }
      }
    }

    function clearFile() {
      console.log('Suppression du fichier');
      const fileInput = document.getElementById('recu');
      if (fileInput) {
        fileInput.value = '';
      }
      resetFilePreview();
    }

    function resetFilePreview() {
      const filePreview = document.getElementById('filePreview');
      const uploadText = document.getElementById('uploadText');

      if (filePreview) {
        filePreview.style.display = 'none';
        filePreview.innerHTML = '';
      }

      if (uploadText) {
        uploadText.innerHTML = '📎 Cliquez pour sélectionner un fichier<br><small>Images, PDF, Word - Max 5MB</small>';
      }
    }

    function formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Fermer les modals en cliquant sur l'overlay
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('form-overlay')) {
        console.log('Clic sur overlay, fermeture du modal');
        closeModal(e.target.id);
      }
    });

    // Gestion de la touche Escape pour fermer les modals
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const openModals = document.querySelectorAll('.form-overlay[style*="flex"]');
        openModals.forEach(modal => {
          closeModal(modal.id);
        });
      }
    });

    // Vérification au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM chargé - Vérification des éléments');

      // Vérifier les boutons
      const btnFacture = document.querySelector('button[onclick*="factureForm"]');
      const btnVirement = document.querySelector('button[onclick*="virementForm"]');

      console.log('Bouton facture trouvé:', !!btnFacture);
      console.log('Bouton virement trouvé:', !!btnVirement);

      // Vérifier les modals
      const modalFacture = document.getElementById('factureForm');
      const modalVirement = document.getElementById('virementForm');
      const modalEditFacture = document.getElementById('editFactureForm');

      console.log('Modal facture trouvé:', !!modalFacture);
      console.log('Modal virement trouvé:', !!modalVirement);
      console.log('Modal modification facture trouvé:', !!modalEditFacture);

      // Test des fonctions
      if (btnFacture) {
        btnFacture.addEventListener('click', function() {
          console.log('Clic sur bouton facture détecté');
        });
      }

      if (btnVirement) {
        btnVirement.addEventListener('click', function() {
          console.log('Clic sur bouton virement détecté');
        });
      }

      // Gérer la surbrillance de la facture
      const urlParams = new URLSearchParams(window.location.search);
      const highlightFactureId = urlParams.get('highlight_facture');
      if (highlightFactureId) {
        const row = document.getElementById(`facture-row-${highlightFactureId}`);
        if (row) {
          row.classList.add('highlight-row');
          row.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
          // Supprimer la classe après un certain temps
          setTimeout(() => {
            row.classList.remove('highlight-row');
          }, 3000); // 3 secondes
        }
      }
    });
  </script>
</body>

</html>
