@echo off
chcp 65001 >nul
color 0C
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🔄 RENOMMAGE EN CYBERPARCAPP 🔄                ║
echo ║                 Mise à jour de l'application                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 Ce script va renommer l'application en "CyberParcApp"
echo.
echo 📝 Modifications qui seront apportées :
echo    - Tous les titres et noms d'application
echo    - Email administrateur : <EMAIL>
echo    - Documentation et guides
echo    - Scripts de création de package
echo.

set /p confirm="Continuer avec le renommage ? (O/N) : "
if /i not "%confirm%"=="O" if /i not "%confirm%"=="Y" (
    echo ❌ Opération annulée
    pause
    exit /b
)

echo.
echo 🚀 Renommage en cours...
echo.

:: C<PERSON>er un script PowerShell pour le remplacement
echo $files = @( > temp_rename.ps1
echo     "accueil.php", >> temp_rename.ps1
echo     "ajouter_entreprise.php", >> temp_rename.ps1
echo     "ajouter_facture.php", >> temp_rename.ps1
echo     "notifications.php", >> temp_rename.ps1
echo     "SETUP_CLIENT\CHECKLIST.md", >> temp_rename.ps1
echo     "SETUP_CLIENT\RACCOURCIS_ADMIN.html", >> temp_rename.ps1
echo     "SETUP_CLIENT\verifier_installation.php", >> temp_rename.ps1
echo     "SETUP_CLIENT\test_final.php", >> temp_rename.ps1
echo     "SETUP_CLIENT\database.php", >> temp_rename.ps1
echo     "CORRECTIONS_APPORTEES.md", >> temp_rename.ps1
echo     "COMMENT_CREER_PACKAGE.md", >> temp_rename.ps1
echo     "SCHEMA_DOSSIER_ZIP.md" >> temp_rename.ps1
echo ^) >> temp_rename.ps1
echo. >> temp_rename.ps1
echo foreach ^($file in $files^) { >> temp_rename.ps1
echo     if ^(Test-Path $file^) { >> temp_rename.ps1
echo         Write-Host "Mise à jour : $file" >> temp_rename.ps1
echo         ^(Get-Content $file^) -replace 'CyberParc(?!App)', 'CyberParcApp' ^| Set-Content $file >> temp_rename.ps1
echo         ^(Get-Content $file^) -replace 'admin@cyberparc\.com', '<EMAIL>' ^| Set-Content $file >> temp_rename.ps1
echo         ^(Get-Content $file^) -replace 'Cyber Parc', 'CyberParcApp' ^| Set-Content $file >> temp_rename.ps1
echo     } >> temp_rename.ps1
echo } >> temp_rename.ps1

echo 📄 Mise à jour des fichiers PHP et documentation...
powershell -ExecutionPolicy Bypass -File temp_rename.ps1

:: Nettoyer le script temporaire
del temp_rename.ps1

echo.
echo ✅ RENOMMAGE TERMINÉ !
echo.
echo 📊 Modifications apportées :
echo    ✅ Application renommée en "CyberParcApp"
echo    ✅ Email admin : <EMAIL>
echo    ✅ Documentation mise à jour
echo    ✅ Scripts de package mis à jour
echo.

echo 🎯 Prochaines étapes :
echo    1. Testez l'application avec le nouveau nom
echo    2. Créez un nouveau package client si nécessaire
echo    3. Mettez à jour vos communications client
echo.

echo 💡 Pour créer un package avec le nouveau nom :
echo    - Utilisez CREER_DOSSIER_BUREAU.bat
echo    - Ou CREER_ICI.bat
echo.

pause
echo.
echo 🎉 CyberParcApp est prêt !
pause
