<?php
session_start();

// Configuration sécurisée
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Inclure la configuration
require_once 'config/database.php';

$local = isset($_SESSION['utilisateur']['local']) ? $_SESSION['utilisateur']['local'] : '';
if (empty($local)) {
  die("Local non défini. Veuillez vous connecter.");
}

try {
  $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
  error_log("Erreur de connexion à la base de données : " . $e->getMessage());
  die("Une erreur est survenue. Veuillez réessayer plus tard.");
}

$today = date('Y-m-d');
$utilisateurId = isset($_SESSION['utilisateur']['id']) ? $_SESSION['utilisateur']['id'] : null;
if (!$utilisateurId) {
  die("Utilisateur non connecté");
}

// Vérifier si la colonne date_creation existe dans la table notifications
$columnsNotif = $pdo->query("SHOW COLUMNS FROM notifications")->fetchAll(PDO::FETCH_COLUMN);
$hasDateCreationNotif = in_array('date_creation', $columnsNotif);

// ========== SUPPRIMER LES ANCIENNES ALERTES DEVENUES OBSOLÈTES (GÉNÉRAL) ==========
$stmtCleanup = $pdo->prepare("
    DELETE n FROM notifications n
    JOIN (
        SELECT 
            e.id AS entreprise_id,
            (COALESCE(SUM(f.Montant), 0) - COALESCE(SUM(v.Montant), 0)) as solde_restant
        FROM entreprises e
        LEFT JOIN facture f ON e.id = f.entreprise_id
        LEFT JOIN virement v ON e.id = v.entreprise_id
        WHERE e.local = ? 
        GROUP BY e.id
        HAVING solde_restant <= 0
    ) AS entreprises_ok ON n.entreprise_id = entreprises_ok.entreprise_id
    WHERE n.utilisateur_id = ? 
    AND n.type = 'retard_paiement_critique'
");
$stmtCleanup->execute([$local, $utilisateurId]);

// Supprimer les anciennes alertes de type 'facture_expiree' si elles existent
$stmtCleanupOldInvoiceAlerts = $pdo->prepare("
    DELETE FROM notifications 
    WHERE utilisateur_id = ? AND type = 'facture_expiree'
");
$stmtCleanupOldInvoiceAlerts->execute([$utilisateurId]);

// ========== NOUVELLES ALERTES : ENTREPRISES AVEC SOLDE IMPAYÉ ET FACTURE ANCIENNE (> 3 MOIS) ==========
$stmtEntreprisesImpayeesAnciennes = $pdo->prepare("
    SELECT
        e.id AS entreprise_id,
        e.nom AS entreprise_nom,
        COALESCE(SUM(f.Montant), 0) AS total_factures,
        COALESCE(SUM(v.Montant), 0) AS total_virements,
        (COALESCE(SUM(f.Montant), 0) - COALESCE(SUM(v.Montant), 0)) AS solde_restant,
        MIN(f.dateEmission) AS premiere_facture_date,
        DATEDIFF(CURDATE(), MIN(f.dateEmission)) AS jours_depuis_premiere_facture
    FROM
        entreprises e
    LEFT JOIN
        facture f ON e.id = f.entreprise_id
    LEFT JOIN
        virement v ON e.id = v.entreprise_id
    WHERE
        e.local = ?
        AND e.active = 1
    GROUP BY
        e.id, e.nom
    HAVING
        solde_restant > 0
        AND jours_depuis_premiere_facture > 90;
");
$stmtEntreprisesImpayeesAnciennes->execute([$local]);
$entreprisesImpayeesAnciennes = $stmtEntreprisesImpayeesAnciennes->fetchAll(PDO::FETCH_ASSOC);
$stmtEntreprisesImpayeesAnciennes->closeCursor();

foreach ($entreprisesImpayeesAnciennes as $entreprise) {
  if ($hasDateCreationNotif) {
    $verifAlerteExistante = $pdo->prepare("
            SELECT COUNT(*) FROM notifications 
            WHERE utilisateur_id = ? AND entreprise_id = ? 
            AND type = 'facture_trimestre_impayee'
            AND date_creation > DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");
  } else {
    $verifAlerteExistante = $pdo->prepare("
            SELECT COUNT(*) FROM notifications 
            WHERE utilisateur_id = ? AND entreprise_id = ? 
            AND type = 'facture_trimestre_impayee'
        ");
  }

  $verifAlerteExistante->execute([$utilisateurId, $entreprise['entreprise_id']]);
  $alerteExistanteCount = $verifAlerteExistante->fetchColumn();
  $verifAlerteExistante->closeCursor();



  if ($alerteExistanteCount == 0) {
    $soldeFormate = number_format($entreprise['solde_restant'], 2, ',', ' ');
    $joursRetard = $entreprise['jours_depuis_premiere_facture'];
    $moisRetard = round($joursRetard / 30.44, 1);

    $msgAlerte = "🔔 ALERTE : {$entreprise['entreprise_nom']} a un solde impayé de {$soldeFormate} DT. La plus ancienne facture date de " . date('d/m/Y', strtotime($entreprise['premiere_facture_date'])) . " (il y a {$moisRetard} mois).";

    if ($hasDateCreationNotif) {
      $insertAlerte = $pdo->prepare("
                INSERT INTO notifications (utilisateur_id, entreprise_id, facture_id, type, message, vu, date_creation)
                VALUES (?, ?, NULL, 'facture_trimestre_impayee', ?, 0, NOW())
            ");
    } else {
      $insertAlerte = $pdo->prepare("
                INSERT INTO notifications (utilisateur_id, entreprise_id, facture_id, type, message, vu)
                VALUES (?, ?, NULL, 'facture_trimestre_impayee', ?, 0)
            ");
    }

    $result = $insertAlerte->execute([
      $utilisateurId,
      $entreprise['entreprise_id'],
      $msgAlerte
    ]);


  }
}

// Pour l'affichage des entreprises
$stmtEntreprisesSoldes = $pdo->prepare("
    SELECT 
        e.id AS entreprise_id,
        e.nom AS entreprise_nom,
        e.chef AS entreprise_chef,
        e.email AS entreprise_email,
        e.numero AS entreprise_numero,
        e.local AS entreprise_local,
        e.active AS entreprise_active,
        e.logo AS logo,
        COALESCE(SUM(f.Montant), 0) as total_factures,
        COALESCE(SUM(v.Montant), 0) as total_virements,
        (COALESCE(SUM(f.Montant), 0) - COALESCE(SUM(v.Montant), 0)) as solde_restant,
        MIN(f.dateEmission) as premiere_facture_date,
        MAX(f.dateEmission) as derniere_facture_date,
        DATEDIFF(CURDATE(), MIN(f.dateEmission)) as jours_depuis_premiere_facture
    FROM entreprises e
    LEFT JOIN facture f ON e.id = f.entreprise_id
    LEFT JOIN virement v ON e.id = v.entreprise_id
    WHERE e.local = ?
    GROUP BY e.id, e.nom, e.chef, e.email, e.numero, e.local, e.active, e.logo
    ORDER BY e.active DESC, e.nom ASC
");
$stmtEntreprisesSoldes->execute([$local]);
$entreprisesData = $stmtEntreprisesSoldes->fetchAll(PDO::FETCH_ASSOC);
$stmtEntreprisesSoldes->closeCursor();

// Messages de succès
$successMessage = '';
if (isset($_GET['success'])) {
  switch ($_GET['success']) {
    case 'entreprise_modifiee':
      $successMessage = 'Entreprise modifiée avec succès !';
      break;
    case 'entreprise_ajoutee':
      $successMessage = 'Entreprise ajoutée avec succès !';
      break;
  }
}
?>

<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8">
  <title>Accueil | Cyber Parc Djerba</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, #1e3c72, #2a5298);
      color: white;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.3);
      padding: 1rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      backdrop-filter: blur(10px);
    }

    h1 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 700;
    }

    .logout-btn {
      color: #fff;
      text-decoration: none;
      font-weight: 600;
      background: #e74c3c;
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .logout-btn:hover {
      background: #c0392b;
      transform: translateY(-2px);
    }

    .container {
      position: relative;
      z-index: 1;
      max-width: 1200px;
      margin: 2rem auto;
      padding: 2rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
    }

    .btn {
      background: linear-gradient(135deg, #1e90ff, #1e3c72);
      color: white;
      padding: 0.875rem 1.75rem;
      border: none;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(30, 144, 255, 0.3);
    }

    .btn:disabled {
      background: #6b7280;
      cursor: not-allowed;
      transform: none;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .btn-success {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    .btn-danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .btn-small {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }

    .companies-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }

    .company-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 1.5rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }

    .company-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .company-card.inactive {
      background: rgba(239, 68, 68, 0.1);
      border: 2px solid rgba(239, 68, 68, 0.3);
    }

    .company-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .company-logo {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      object-fit: cover;
      border: 2px solid #e5e7eb;
      flex-shrink: 0;
    }

    .no-logo {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      background: #f3f4f6;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: #6b7280;
      border: 2px dashed #d1d5db;
      flex-shrink: 0;
    }

    .company-info h3 {
      color: #1e3c72;
      font-size: 1.2rem;
      font-weight: 700;
      margin: 0;
      line-height: 1.3;
    }

    .company-info p {
      color: #6b7280;
      font-size: 0.9rem;
      margin: 0.25rem 0 0 0;
    }

    .company-details {
      color: #374151;
      font-size: 0.9rem;
      margin-bottom: 1.5rem;
    }

    .company-details div {
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .status-badge {
      position: absolute;
      top: 1rem;
      right: 1rem;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .status-active {
      background: #10b981;
      color: white;
    }

    .status-inactive {
      background: #ef4444;
      color: white;
    }

    .company-actions {
      display: flex;
      gap: 0.75rem;
      flex-wrap: wrap;
    }

    .company-actions .btn {
      flex: 1;
      min-width: 100px;
      justify-content: center;
    }

    .alert {
      padding: 1rem;
      border-radius: 12px;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
    }

    .alert-success {
      background: #f0fdf4;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      color: rgba(255, 255, 255, 0.7);
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    /* Modal */
    .modal {
      display: none;
      position: fixed;
      z-index: 999;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(5px);
      justify-content: center;
      align-items: center;
    }

    .modal-content {
      background: white;
      color: #333;
      padding: 2rem;
      border-radius: 20px;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .close {
      position: absolute;
      top: 1rem;
      right: 1rem;
      font-size: 1.5rem;
      color: #6b7280;
      cursor: pointer;
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .close:hover {
      background: #f3f4f6;
      color: #374151;
    }

    .modal h3 {
      color: #1e3c72;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      font-weight: 700;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: #374151;
    }

    .required {
      color: #dc2626;
    }

    input[type="text"],
    input[type="email"],
    input[type="tel"],
    select {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-family: 'Inter', sans-serif;
      font-size: 1rem;
      transition: border-color 0.2s ease;
    }

    input:focus,
    select:focus {
      outline: none;
      border-color: #1e90ff;
      box-shadow: 0 0 0 3px rgba(30, 144, 255, 0.1);
    }

    .file-input-container {
      position: relative;
      width: 100%;
    }

    .file-input {
      width: 100%;
      padding: 1rem;
      border: 2px dashed #d1d5db;
      border-radius: 12px;
      background: #f9fafb;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .file-input:hover {
      border-color: #1e90ff;
      background: #eff6ff;
    }

    .file-input input[type="file"] {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }

    .file-preview {
      margin-top: 1rem;
      text-align: center;
    }

    .file-preview img {
      max-width: 150px;
      max-height: 150px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .file-info {
      font-size: 0.8rem;
      color: #6b7280;
      margin-top: 0.5rem;
    }

    @media (max-width: 768px) {
      .container {
        margin: 1rem;
        padding: 1.5rem;
      }

      .companies-grid {
        grid-template-columns: 1fr;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }

      .company-actions {
        flex-direction: column;
      }

      .company-actions .btn {
        flex: none;
      }
    }
  </style>
</head>

<body>
  <header>
    <h1>🏢 Cyber Parc <?= htmlspecialchars($local) ?></h1>
    <div style="display: flex; align-items: center; gap: 1.5rem;">
      <!-- Bouton Gérer les notifications -->
      <a href="notifications.php" class="btn btn-secondary btn-small">
         les notifications
      </a>
      <!-- Bouton Déconnexion -->
      <a href="deconnexion.php" class="logout-btn">🚪 Déconnexion</a>
    </div>
  </header>

  <div class="container">
    <!-- Messages de succès -->
    <?php if ($successMessage): ?>
      <div class="alert alert-success">
        <span>✅</span>
        <?= htmlspecialchars($successMessage) ?>
      </div>
    <?php endif; ?>

    <div class="section-header">
      <div>
        <h2 class="section-title">Liste des entreprises</h2>
        <p style="color: rgba(255, 255, 255, 0.8); margin-top: 0.5rem;">
          Gérez les entreprises de votre cyber parc
        </p>
      </div>
      <button class="btn" onclick="ouvrirModalAjouter()">
        ➕ Ajouter une entreprise
      </button>
    </div>

    <div class="companies-grid">
      <?php if (empty($entreprisesData)): ?>
        <div class="empty-state" style="grid-column: 1 / -1;">
          <div class="empty-state-icon">🏢</div>
          <h3>Aucune entreprise</h3>
          <p>Commencez par ajouter votre première entreprise au cyber parc.</p>
        </div>
      <?php else: ?>
        <?php foreach ($entreprisesData as $e): ?>
          <?php $active = isset($e['entreprise_active']) && $e['entreprise_active'] == 1; ?>
          <div class="company-card <?= !$active ? 'inactive' : '' ?>" id="entreprise-<?= $e['entreprise_id'] ?>">
            <div class="status-badge <?= $active ? 'status-active' : 'status-inactive' ?>">
              <?php if ($active): ?>
                ✅ Active
              <?php else: ?>
                ❌ Inactive
              <?php endif; ?>
            </div>

            <div class="company-header">
              <div class="company-logo-container">
                <?php if (!empty($e['logo']) && file_exists('uploads/logos/' . $e['logo'])): ?>
                  <img src="uploads/logos/<?= htmlspecialchars($e['logo']) ?>"
                    alt="Logo <?= htmlspecialchars($e['entreprise_nom']) ?>"
                    class="company-logo">
                <?php else: ?>
                  <div class="no-logo">🏢</div>
                <?php endif; ?>
              </div>
              <div class="company-info">
                <h3><?= htmlspecialchars($e['entreprise_nom']) ?></h3>
                <p><?= htmlspecialchars($e['entreprise_chef']) ?></p>
              </div>
            </div>

            <div class="company-details">
              <div>📧 <?= htmlspecialchars($e['entreprise_email']) ?></div>
              <div>📞 <?= htmlspecialchars($e['entreprise_numero']) ?></div>
              <?php if (isset($e['entreprise_local'])): ?>
                <div>📍 <?= htmlspecialchars($e['entreprise_local']) ?></div>
              <?php endif; ?>
            </div>

            <div class="company-actions">
              <?php if ($active): ?>
                <a href="details_entreprise.php?id=<?= $e['entreprise_id'] ?>" class="btn btn-secondary btn-small">
                  👁 Détails
                </a>
                <button class="btn btn-small"
                  onclick="ouvrirModalModifier(
                    <?= $e['entreprise_id'] ?>,
                    '<?= addslashes(htmlspecialchars($e['entreprise_nom'])) ?>',
                    '<?= addslashes(htmlspecialchars($e['entreprise_chef'])) ?>',
                    '<?= addslashes(htmlspecialchars($e['entreprise_numero'])) ?>',
                    '<?= addslashes(htmlspecialchars($e['entreprise_email'])) ?>'
                
                  )">
                  ✏ Modifier
                </button>
                <form method="post" action="changer_etat_entreprise.php"
                  onsubmit="return confirm('Voulez-vous désactiver cette entreprise ?');"
                  style="display: inline;">
                  <input type="hidden" name="id" value="<?= $e['entreprise_id'] ?>">
                  <input type="hidden" name="action" value="desactiver">
                  <button type="submit" class="btn btn-danger btn-small">
                    ❌ Désactiver
                  </button>
                </form>
              <?php else: ?>
                <button class="btn btn-secondary btn-small" disabled title="Entreprise inactive">
                  👁 Détails
                </button>
                <button class="btn btn-small" disabled title="Entreprise inactive">
                  ✏ Modifier
                </button>
                <form method="post" action="changer_etat_entreprise.php"
                  onsubmit="return confirm('Voulez-vous activer cette entreprise ?');"
                  style="display: inline;">
                  <input type="hidden" name="id" value="<?= $e['entreprise_id'] ?>">
                  <input type="hidden" name="action" value="activer">
                  <button type="submit" class="btn btn-success btn-small">
                    ✅ Activer
                  </button>
                </form>
              <?php endif; ?>
            </div>
          </div>
        <?php endforeach; ?>
      <?php endif; ?>
    </div>
  </div>

  <!-- Modal Ajouter entreprise -->
  <div class="modal" id="modalAjouter">
    <div class="modal-content">
      <span class="close" onclick="fermerModalAjouter()">&times;</span>
      <h3>🏢 Nouvelle entreprise</h3>
      <form action="ajouter_entreprise.php" method="post" enctype="multipart/form-data" id="formAjouter">
        <div class="form-group">
          <label for="nom">Nom de l'entreprise <span class="required">*</span></label>
          <input type="text" id="nom" name="nom" required placeholder="Ex: TechSoft Solutions">
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="chef">Chef d'entreprise <span class="required">*</span></label>
            <input type="text" id="chef" name="chef" required placeholder="Ex: Ahmed Ben Ali">
          </div>
          <div class="form-group">
            <label for="numero">Téléphone <span class="required">*</span></label>
            <input type="tel" id="numero" name="numero" required placeholder="Ex: 98765432">
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email <span class="required">*</span></label>
          <input type="email" id="email" name="email" required placeholder="Ex: <EMAIL>">
        </div>

        <div class="form-group">
          <label for="logo">Logo de l'entreprise</label>
          <div class="file-input-container">
            <div class="file-input" onclick="document.getElementById('logo').click()">
              <input type="file" id="logo" name="logo" accept="image/*" onchange="previewLogo(this)">
              <div class="file-input-text">
                📷 Cliquez pour sélectionner un logo<br>
                <small>JPG, PNG, GIF, WebP - Max 2MB</small>
              </div>
            </div>
            <div class="file-info">
              Le logo sera automatiquement redimensionné à 300x300px maximum
            </div>
            <div id="logoPreview" class="file-preview"></div>
          </div>
        </div>

        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
          <button class="btn" type="submit" style="flex: 1;">
            ✅ Ajouter l'entreprise
          </button>
          <button type="button" class="btn btn-secondary" onclick="fermerModalAjouter()" style="flex: 1;">
            ❌ Annuler
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal Modifier entreprise -->
  <div class="modal" id="modalModifier">
    <div class="modal-content">
      <span class="close" onclick="fermerModalModifier()">&times;</span>
      <h3>✏ Modifier l'entreprise</h3>
      <form action="modifier_entreprise.php" method="post" id="formModifier">
        <input type="hidden" name="id" id="mod_id">

        <div class="form-group">
          <label for="mod_nom">Nom de l'entreprise <span class="required">*</span></label>
          <input type="text" id="mod_nom" name="nom" required>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="mod_chef">Chef d'entreprise <span class="required">*</span></label>
            <input type="text" id="mod_chef" name="chef" required>
          </div>
          <div class="form-group">
            <label for="mod_numero">Téléphone <span class="required">*</span></label>
            <input type="tel" id="mod_numero" name="numero" required>
          </div>
        </div>

        <div class="form-group">
          <label for="mod_email">Email <span class="required">*</span></label>
          <input type="email" id="mod_email" name="email" required>
        </div>

        <div style="display: flex; gap: 1rem; margin-top: 2rem;">
          <button class="btn" type="submit" style="flex: 1;">
            ✅ Enregistrer les modifications
          </button>
          <button type="button" class="btn btn-secondary" onclick="fermerModalModifier()" style="flex: 1;">
            ❌ Annuler
          </button>
        </div>
      </form>
    </div>
  </div>

  <script>
    function ouvrirModalAjouter() {
      document.getElementById("modalAjouter").style.display = "flex";
      document.body.style.overflow = "hidden";
    }

    function fermerModalAjouter() {
      document.getElementById("modalAjouter").style.display = "none";
      document.body.style.overflow = "auto";

      document.getElementById("formAjouter").reset();
      document.getElementById("logoPreview").innerHTML = "";
      document.querySelector(".file-input-text").innerHTML = '📷 Cliquez pour sélectionner un logo<br><small>JPG, PNG, GIF, WebP - Max 2MB</small>';
    }

    
function ouvrirModalModifier(id, nom, chef, numero, email) {
  document.getElementById('mod_id').value = id;
  document.getElementById('mod_nom').value = nom;
  document.getElementById('mod_chef').value = chef;
  document.getElementById('mod_numero').value = numero;
  document.getElementById('mod_email').value = email;
  
  // MODIFICATION ICI : Changer l'action du formulaire pour inclure l'ID
  const form = document.getElementById('formModifier');
  form.action = 'modifier_entreprise.php?id=' + id;
  
  document.getElementById('modalModifier').style.display = 'flex';
  document.body.style.overflow = "hidden";
}

    function fermerModalModifier() {
      document.getElementById('modalModifier').style.display = 'none';
      document.body.style.overflow = "auto";
    }

    function previewLogo(input) {
      const preview = document.getElementById('logoPreview');
      const fileInputText = document.querySelector('.file-input-text');

      if (input.files && input.files[0]) {
        const file = input.files[0];

        if (file.size > 2097152) {
          alert('Le fichier est trop volumineux. Taille maximum : 2MB');
          input.value = '';
          return;
        }

        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          alert('Type de fichier non autorisé. Utilisez JPG, PNG, GIF ou WebP.');
          input.value = '';
          return;
        }

        const reader = new FileReader();

        reader.onload = function(e) {
          preview.innerHTML = `
            <img src="${e.target.result}" alt="Aperçu du logo">
            <p><strong>${file.name}</strong></p>
            <small>${formatFileSize(file.size)}</small>
          `;
          fileInputText.innerHTML = '✅ Logo sélectionné<br><small>Cliquez pour changer</small>';
        };

        reader.readAsDataURL(file);
      } else {
        preview.innerHTML = '';
        fileInputText.innerHTML = '📷 Cliquez pour sélectionner un logo<br><small>JPG, PNG, GIF, WebP - Max 2MB</small>';
      }
    }

    function formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Fermer les modales en cliquant à l'extérieur
    window.onclick = function(event) {
      const modalAjouter = document.getElementById("modalAjouter");
      const modalModifier = document.getElementById("modalModifier");

      if (event.target == modalAjouter) {
        fermerModalAjouter();
      }
      if (event.target == modalModifier) {
        fermerModalModifier();
      }
    }

    // Validation des formulaires
    document.getElementById('formAjouter').addEventListener('submit', function(e) {
      const requiredFields = ['nom', 'chef', 'numero', 'email'];
      let hasError = false;

      requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (!input.value.trim()) {
          input.style.borderColor = '#dc2626';
          hasError = true;
        } else {
          input.style.borderColor = '#e5e7eb';
        }
      });

      if (hasError) {
        e.preventDefault();
        alert('Veuillez remplir tous les champs obligatoires');
      }
    });

    document.getElementById('formModifier').addEventListener('submit', function(e) {
      const requiredFields = ['mod_nom', 'mod_chef', 'mod_numero', 'mod_email'];
      let hasError = false;

      requiredFields.forEach(field => {
        const input = document.getElementById(field);
        if (!input.value.trim()) {
          input.style.borderColor = '#dc2626';
          hasError = true;
        } else {
          input.style.borderColor = '#e5e7eb';
        }
      });

      if (hasError) {
        e.preventDefault();
        alert('Veuillez remplir tous les champs obligatoires');
      }
    });
  </script>

</body>

</html>