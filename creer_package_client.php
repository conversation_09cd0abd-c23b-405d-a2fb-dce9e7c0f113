<?php
/**
 * Script PHP pour créer le package client CyberParc
 * Alternative au script BAT pour les systèmes non-Windows
 */

echo "🚀 Création du package client CyberParc...\n\n";

// Configuration
$packageDir = 'CYBERPARC_CLIENT_PACKAGE';
$zipFile = 'CyberParc_Client_Package.zip';

// Supprimer le dossier existant
if (is_dir($packageDir)) {
    deleteDirectory($packageDir);
}

// Créer la structure
$dirs = [
    $packageDir,
    "$packageDir/config",
    "$packageDir/uploads",
    "$packageDir/uploads/logos",
    "$packageDir/uploads/recus",
    "$packageDir/vendor",
    "$packageDir/DOCUMENTATION"
];

foreach ($dirs as $dir) {
    if (!mkdir($dir, 0755, true)) {
        die("❌ Erreur création dossier: $dir\n");
    }
}

echo "📁 Structure créée\n";

// Fichiers à copier
$filesToCopy = [
    // Fichiers PHP principaux
    '*.php' => $packageDir,
    '*.ico' => $packageDir,
    '*.jpg' => $packageDir,
    'composer.json' => $packageDir,
    'composer.lock' => $packageDir,
    
    // Base de données
    'SETUP_CLIENT/stage.sql' => $packageDir,
    
    // Configuration
    'SETUP_CLIENT/database.php' => "$packageDir/config",
    
    // Documentation
    'SETUP_CLIENT/README.md' => $packageDir,
    'SETUP_CLIENT/GUIDE_RAPIDE.html' => $packageDir,
    'SETUP_CLIENT/CHECKLIST.md' => "$packageDir/DOCUMENTATION",
    'SETUP_CLIENT/DOCUMENTATION_COMPLETE.md' => "$packageDir/DOCUMENTATION",
    'SETUP_CLIENT/RACCOURCIS_ADMIN.html' => "$packageDir/DOCUMENTATION",
    
    // Outils
    'SETUP_CLIENT/verifier_installation.php' => $packageDir,
    'SETUP_CLIENT/INSTALLATION_RAPIDE.bat' => $packageDir,
];

// Copier les fichiers
foreach ($filesToCopy as $source => $dest) {
    if (strpos($source, '*') !== false) {
        // Gestion des wildcards
        $pattern = str_replace('*', '', $source);
        $files = glob($source);
        foreach ($files as $file) {
            if (is_file($file)) {
                copy($file, "$dest/" . basename($file));
            }
        }
    } else {
        if (file_exists($source)) {
            $destFile = is_dir($dest) ? "$dest/" . basename($source) : $dest;
            copy($source, $destFile);
        }
    }
}

echo "📄 Fichiers copiés\n";

// Copier le dossier vendor si il existe
if (is_dir('vendor')) {
    copyDirectory('vendor', "$packageDir/vendor");
    echo "📦 Dépendances Composer copiées\n";
} else {
    echo "⚠️ Dossier vendor non trouvé\n";
}

// Créer le fichier LISEZ-MOI
$readmeContent = <<<'README'
# 🚀 CyberParc - Système de Gestion Comptable

## 📋 Contenu du package

### Fichiers principaux :
- **index.php** : Page de connexion
- **accueil.php** : Tableau de bord principal
- **stage.sql** : Base de données à importer
- **config/database.php** : Configuration à modifier

### Documentation :
- **README.md** : Guide d'installation rapide
- **GUIDE_RAPIDE.html** : Guide visuel (ouvrir dans un navigateur)
- **DOCUMENTATION/** : Documentation complète

### Outils :
- **INSTALLATION_RAPIDE.bat** : Installation automatique Windows
- **verifier_installation.php** : Vérification du système

## 🚀 Installation Rapide

### Étape 1 : Préparer la base de données
1. Ouvrez phpMyAdmin
2. Créez une base de données nommée `stage`
3. Importez le fichier `stage.sql`

### Étape 2 : Configuration
1. Modifiez le fichier `config/database.php`
2. Changez les paramètres de connexion MySQL
3. Configurez l'email (optionnel)

### Étape 3 : Test
1. Accédez à votre site
2. Connectez-vous avec : **<EMAIL>** / **admin123**
3. **CHANGEZ IMMÉDIATEMENT LE MOT DE PASSE !**

## 📞 Support

- Consultez **GUIDE_RAPIDE.html** pour un guide visuel
- Utilisez **verifier_installation.php** pour diagnostiquer les problèmes
- Référez-vous à la **DOCUMENTATION/** pour les détails techniques

## 🔒 Sécurité

⚠️ **IMPORTANT** : Changez le mot de passe administrateur dès la première connexion !

---
**Version** : CyberParc v1.0  
**Date** : Janvier 2025  
**Support** : Consultez la documentation incluse
README;

file_put_contents("$packageDir/LISEZ-MOI.md", $readmeContent);

// Créer le script d'installation express
$installScript = <<<'INSTALL'
@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 CYBERPARC SETUP 🚀                    ║
echo ║              Installation Express - 5 minutes               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📋 ÉTAPES D'INSTALLATION :
echo.
echo 1️⃣  Importez 'stage.sql' dans phpMyAdmin
echo 2️⃣  Modifiez 'config\database.php' avec vos paramètres
echo 3️⃣  Accédez à votre site web
echo 4️⃣  Connectez-vous : <EMAIL> / admin123
echo 5️⃣  CHANGEZ LE MOT DE PASSE IMMÉDIATEMENT !
echo.
echo 🔧 OUTILS DISPONIBLES :
echo    - GUIDE_RAPIDE.html (guide visuel)
echo    - verifier_installation.php (diagnostic)
echo    - DOCUMENTATION\ (aide complète)
echo.
echo 📞 En cas de problème, consultez README.md
echo.
pause
INSTALL;

file_put_contents("$packageDir/INSTALLATION_EXPRESS.bat", $installScript);

// Créer le test rapide
$testContent = <<<'TEST'
<?php
// Vérification rapide - CyberParc
echo "<h1>🔍 Vérification Rapide CyberParc</h1>";
if (file_exists('config/database.php')) {
    echo "<p style='color:green'>✅ Configuration trouvée</p>";
    require_once 'config/database.php';
    try {
        $pdo = new PDO($dsn, $username, $password, $options);
        echo "<p style='color:green'>✅ Connexion base de données OK</p>";
        echo "<p><a href='index.php'>👉 Accéder au système</a></p>";
    } catch (Exception $e) {
        echo "<p style='color:red'>❌ Erreur BDD: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color:red'>❌ Configuration manquante</p>";
}
?>
TEST;

file_put_contents("$packageDir/test_rapide.php", $testContent);

echo "📝 Fichiers supplémentaires créés\n";

// Créer l'archive ZIP si possible
if (class_exists('ZipArchive')) {
    $zip = new ZipArchive();
    if ($zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
        addDirectoryToZip($zip, $packageDir, '');
        $zip->close();
        echo "🗜️ Archive créée: $zipFile\n";
    } else {
        echo "❌ Erreur création ZIP\n";
    }
} else {
    echo "⚠️ Extension ZIP non disponible - Archive manuelle nécessaire\n";
}

echo "\n✅ Package créé avec succès !\n\n";
echo "📦 Dossier : $packageDir/\n";
echo "📧 Archive : $zipFile\n\n";
echo "🎯 PRÊT À ENVOYER AU CLIENT !\n\n";
echo "💡 Instructions pour le client :\n";
echo "   1. Décompresser l'archive\n";
echo "   2. Lire LISEZ-MOI.md\n";
echo "   3. Exécuter INSTALLATION_EXPRESS.bat\n";
echo "   4. Suivre les étapes affichées\n\n";

// Fonctions utilitaires
function deleteDirectory($dir) {
    if (!is_dir($dir)) return;
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = "$dir/$file";
        is_dir($path) ? deleteDirectory($path) : unlink($path);
    }
    rmdir($dir);
}

function copyDirectory($src, $dst) {
    if (!is_dir($src)) return;
    if (!is_dir($dst)) mkdir($dst, 0755, true);
    
    $files = array_diff(scandir($src), ['.', '..']);
    foreach ($files as $file) {
        $srcPath = "$src/$file";
        $dstPath = "$dst/$file";
        
        if (is_dir($srcPath)) {
            copyDirectory($srcPath, $dstPath);
        } else {
            copy($srcPath, $dstPath);
        }
    }
}

function addDirectoryToZip($zip, $dir, $zipPath) {
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $filePath = "$dir/$file";
        $zipFilePath = $zipPath ? "$zipPath/$file" : $file;
        
        if (is_dir($filePath)) {
            $zip->addEmptyDir($zipFilePath);
            addDirectoryToZip($zip, $filePath, $zipFilePath);
        } else {
            $zip->addFile($filePath, $zipFilePath);
        }
    }
}
?>
