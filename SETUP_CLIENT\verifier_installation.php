<?php
/**
 * Script de vérification d'installation CyberParc
 * À exécuter après l'installation pour vérifier que tout fonctionne
 */

// Configuration
$required_php_version = '7.4.0';
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl'];
$required_dirs = ['uploads', 'uploads/logos', 'uploads/recus', 'config'];
$required_files = ['config/database.php', 'index.php', 'accueil.php'];

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Installation - CyberParc</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .check { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .check.success { background: #d4edda; border-left: 4px solid #28a745; }
        .check.error { background: #f8d7da; border-left: 4px solid #dc3545; }
        .check.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        h1 { text-align: center; color: #333; }
        h2 { color: #555; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vérification d'Installation CyberParc</h1>
        
        <h2>📋 Version PHP</h2>
        <?php
        $php_version = phpversion();
        if (version_compare($php_version, $required_php_version, '>=')) {
            echo "<div class='check success'>✅ PHP $php_version (requis: $required_php_version+)</div>";
        } else {
            echo "<div class='check error'>❌ PHP $php_version - Version insuffisante (requis: $required_php_version+)</div>";
        }
        ?>

        <h2>🔧 Extensions PHP</h2>
        <?php
        foreach ($required_extensions as $ext) {
            if (extension_loaded($ext)) {
                echo "<div class='check success'>✅ Extension $ext chargée</div>";
            } else {
                echo "<div class='check error'>❌ Extension $ext manquante</div>";
            }
        }
        ?>

        <h2>📁 Dossiers requis</h2>
        <?php
        foreach ($required_dirs as $dir) {
            if (is_dir($dir)) {
                if (is_writable($dir)) {
                    echo "<div class='check success'>✅ Dossier $dir existe et est accessible en écriture</div>";
                } else {
                    echo "<div class='check warning'>⚠️ Dossier $dir existe mais n'est pas accessible en écriture</div>";
                }
            } else {
                echo "<div class='check error'>❌ Dossier $dir manquant</div>";
            }
        }
        ?>

        <h2>📄 Fichiers requis</h2>
        <?php
        foreach ($required_files as $file) {
            if (file_exists($file)) {
                echo "<div class='check success'>✅ Fichier $file présent</div>";
            } else {
                echo "<div class='check error'>❌ Fichier $file manquant</div>";
            }
        }
        ?>

        <h2>🗄️ Base de données</h2>
        <?php
        if (file_exists('config/database.php')) {
            try {
                require_once 'config/database.php';
                $pdo = new PDO($dsn, $username, $password, $options);
                echo "<div class='check success'>✅ Connexion à la base de données réussie</div>";
                
                // Vérifier les tables principales
                $tables = ['utilisateurs', 'entreprises', 'facture', 'virement'];
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        echo "<div class='check success'>✅ Table $table présente</div>";
                    } else {
                        echo "<div class='check error'>❌ Table $table manquante</div>";
                    }
                }
                
                // Vérifier l'utilisateur admin
                $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateurs WHERE email = '<EMAIL>'");
                if ($stmt->fetchColumn() > 0) {
                    echo "<div class='check success'>✅ Compte administrateur par défaut présent</div>";
                } else {
                    echo "<div class='check warning'>⚠️ Compte administrateur par défaut non trouvé</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='check error'>❌ Erreur de connexion à la base de données: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        } else {
            echo "<div class='check error'>❌ Fichier de configuration de base de données manquant</div>";
        }
        ?>

        <h2>📧 Configuration Email</h2>
        <?php
        if (isset($email_config)) {
            if ($email_config['smtp_username'] !== '<EMAIL>') {
                echo "<div class='check success'>✅ Configuration email personnalisée</div>";
            } else {
                echo "<div class='check warning'>⚠️ Configuration email par défaut (fonctionnalité de récupération de mot de passe non disponible)</div>";
            }
        } else {
            echo "<div class='check warning'>⚠️ Configuration email non trouvée</div>";
        }
        ?>

        <h2>🎯 Résumé</h2>
        <?php
        $errors = substr_count(ob_get_contents(), 'check error');
        $warnings = substr_count(ob_get_contents(), 'check warning');
        
        if ($errors == 0) {
            if ($warnings == 0) {
                echo "<div class='check success'><strong>🎉 Installation parfaite ! Votre système CyberParc est prêt à être utilisé.</strong></div>";
            } else {
                echo "<div class='check warning'><strong>⚠️ Installation fonctionnelle avec $warnings avertissement(s). Le système peut être utilisé mais certaines fonctionnalités peuvent être limitées.</strong></div>";
            }
        } else {
            echo "<div class='check error'><strong>❌ Installation incomplète : $errors erreur(s) détectée(s). Veuillez corriger ces problèmes avant d'utiliser le système.</strong></div>";
        }
        ?>

        <h2>🚀 Prochaines étapes</h2>
        <div class="check">
            <ol>
                <li>Si tout est vert, accédez à <a href="index.php">votre site</a></li>
                <li>Connectez-vous avec : <EMAIL> / admin123</li>
                <li><strong>Changez immédiatement le mot de passe administrateur</strong></li>
                <li>Créez vos comptes utilisateurs</li>
                <li>Commencez à ajouter vos entreprises</li>
            </ol>
        </div>

        <div class="check warning">
            <strong>🔒 Sécurité :</strong> Supprimez ce fichier de vérification après l'installation pour éviter l'exposition d'informations sensibles.
        </div>
    </div>
</body>
</html>
