<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - CyberParc</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .shortcut-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #3498db;
            transition: transform 0.2s;
        }
        .shortcut-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .shortcut-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .shortcut-desc {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .shortcut-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .btn {
            padding: 8px 15px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9em;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .btn.danger:hover {
            background: #c0392b;
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.success:hover {
            background: #229954;
        }
        .btn.warning {
            background: #f39c12;
        }
        .btn.warning:hover {
            background: #e67e22;
        }
        .quick-stats {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Panneau d'Administration CyberParc</h1>
        
        <div class="quick-stats">
            <h2>📊 Statistiques Rapides</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="total-entreprises">-</div>
                    <div class="stat-label">Entreprises</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="factures-impayees">-</div>
                    <div class="stat-label">Factures Impayées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="notifications">-</div>
                    <div class="stat-label">Notifications</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="solde-total">-</div>
                    <div class="stat-label">Solde Total (DT)</div>
                </div>
            </div>
        </div>

        <div class="shortcuts-grid">
            <div class="shortcut-card">
                <div class="shortcut-title">🏢 Gestion des Entreprises</div>
                <div class="shortcut-desc">Ajouter, modifier ou consulter les entreprises de votre parc</div>
                <div class="shortcut-links">
                    <a href="accueil.php" class="btn">Voir toutes</a>
                    <a href="ajouter_entreprise.php" class="btn success">Ajouter</a>
                    <a href="debug_entreprises.php" class="btn warning">Debug</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">📄 Facturation</div>
                <div class="shortcut-desc">Gérer les factures et suivre les paiements</div>
                <div class="shortcut-links">
                    <a href="ajouter_facture.php" class="btn success">Nouvelle facture</a>
                    <a href="modifier_facture.php" class="btn">Modifier</a>
                    <a href="supprimer_facture.php" class="btn danger">Supprimer</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">💰 Virements</div>
                <div class="shortcut-desc">Enregistrer les paiements reçus</div>
                <div class="shortcut-links">
                    <a href="ajouter_virement.php" class="btn success">Nouveau virement</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">🔔 Notifications</div>
                <div class="shortcut-desc">Gérer les alertes et rappels de paiement</div>
                <div class="shortcut-links">
                    <a href="notifications.php" class="btn">Voir toutes</a>
                    <a href="verifier_alerte.php" class="btn warning">Vérifier alertes</a>
                    <a href="marquer_notifications_lues.php" class="btn">Marquer lues</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">💾 Sauvegarde</div>
                <div class="shortcut-desc">Exporter et sauvegarder vos données comptables</div>
                <div class="shortcut-links">
                    <a href="sauver_comptabilite.php" class="btn success">Exporter Excel</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">🔧 Administration</div>
                <div class="shortcut-desc">Outils d'administration et maintenance</div>
                <div class="shortcut-links">
                    <a href="verifier_installation.php" class="btn warning">Vérifier système</a>
                    <a href="debug_entreprises.php" class="btn">Debug entreprises</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">👤 Compte</div>
                <div class="shortcut-desc">Gérer votre profil et sécurité</div>
                <div class="shortcut-links">
                    <a href="motdepasse_oublie.php" class="btn warning">Changer mot de passe</a>
                    <a href="deconnexion.php" class="btn danger">Déconnexion</a>
                </div>
            </div>

            <div class="shortcut-card">
                <div class="shortcut-title">📚 Aide</div>
                <div class="shortcut-desc">Documentation et support</div>
                <div class="shortcut-links">
                    <a href="GUIDE_RAPIDE.html" class="btn">Guide d'utilisation</a>
                    <a href="CHECKLIST.md" class="btn">Checklist</a>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>🕒 Dernière mise à jour : <span id="last-update">-</span></p>
            <p>💡 <strong>Conseil :</strong> Sauvegardez régulièrement vos données et changez votre mot de passe périodiquement</p>
        </div>
    </div>

    <script>
        // Mise à jour de l'heure
        document.getElementById('last-update').textContent = new Date().toLocaleString('fr-FR');

        // Simulation de statistiques (à remplacer par de vraies données via AJAX)
        setTimeout(() => {
            document.getElementById('total-entreprises').textContent = '12';
            document.getElementById('factures-impayees').textContent = '3';
            document.getElementById('notifications').textContent = '5';
            document.getElementById('solde-total').textContent = '15,420';
        }, 500);

        // Ajouter des raccourcis clavier
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        window.location.href = 'accueil.php';
                        break;
                    case '2':
                        e.preventDefault();
                        window.location.href = 'ajouter_entreprise.php';
                        break;
                    case '3':
                        e.preventDefault();
                        window.location.href = 'ajouter_facture.php';
                        break;
                    case '4':
                        e.preventDefault();
                        window.location.href = 'notifications.php';
                        break;
                }
            }
        });

        // Afficher les raccourcis clavier
        console.log('Raccourcis clavier disponibles:');
        console.log('Ctrl+1: Accueil');
        console.log('Ctrl+2: Ajouter entreprise');
        console.log('Ctrl+3: Ajouter facture');
        console.log('Ctrl+4: Notifications');
    </script>
</body>
</html>
