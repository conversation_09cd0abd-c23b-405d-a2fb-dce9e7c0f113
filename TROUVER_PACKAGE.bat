@echo off
chcp 65001 >nul
color 0B
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔍 RECHERCHE PACKAGE CLIENT 🔍               ║
echo ║                  Localisation automatique                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Recherche du package CyberParc...
echo.

:: Vérifier sur le bureau
set "DESKTOP=%USERPROFILE%\Desktop"
echo 📍 Vérification du bureau : %DESKTOP%
if exist "%DESKTOP%\CYBERPARC_CLIENT_PACKAGE" (
    echo ✅ TROUVÉ sur le bureau !
    echo    📁 Dossier : %DESKTOP%\CYBERPARC_CLIENT_PACKAGE
    if exist "%DESKTOP%\CyberParc_Client_Package.zip" (
        echo    📧 Archive : %DESKTOP%\CyberParc_Client_Package.zip
    )
    echo.
    echo 🚀 Ouverture du bureau...
    start "" "%DESKTOP%"
    goto :found
)

:: Vérifier dans le dossier courant
echo 📍 Vérification du dossier courant : %CD%
if exist "CYBERPARC_CLIENT_PACKAGE" (
    echo ✅ TROUVÉ dans le dossier courant !
    echo    📁 Dossier : %CD%\CYBERPARC_CLIENT_PACKAGE
    if exist "CyberParc_Client_Package.zip" (
        echo    📧 Archive : %CD%\CyberParc_Client_Package.zip
    )
    echo.
    echo 🚀 Ouverture du dossier...
    start "" "%CD%"
    goto :found
)

:: Recherche étendue
echo 📍 Recherche étendue sur le disque C:...
for /f "delims=" %%i in ('dir /s /b /ad "C:\CYBERPARC_CLIENT_PACKAGE" 2^>nul') do (
    echo ✅ TROUVÉ : %%i
    echo.
    echo 🚀 Ouverture du dossier parent...
    start "" "%%~dpi"
    goto :found
)

:: Recherche dans les dossiers utilisateur courants
echo 📍 Recherche dans les emplacements courants...
set "LOCATIONS=%USERPROFILE%\Documents %USERPROFILE%\Downloads %USERPROFILE% %USERPROFILE%\OneDrive\Desktop"

for %%L in (%LOCATIONS%) do (
    if exist "%%L\CYBERPARC_CLIENT_PACKAGE" (
        echo ✅ TROUVÉ dans : %%L
        echo    📁 Dossier : %%L\CYBERPARC_CLIENT_PACKAGE
        echo.
        echo 🚀 Ouverture...
        start "" "%%L"
        goto :found
    )
)

:: Non trouvé
echo ❌ Package non trouvé !
echo.
echo 💡 Solutions :
echo    1. Exécutez d'abord PACKAGE_BUREAU.bat pour créer le package
echo    2. Vérifiez que vous êtes dans le bon dossier du projet
echo    3. Le package a peut-être été créé ailleurs
echo.
echo 🔧 Création automatique du package...
echo.
if exist "PACKAGE_BUREAU.bat" (
    call "PACKAGE_BUREAU.bat"
) else if exist "CREER_PACKAGE_CLIENT.bat" (
    call "CREER_PACKAGE_CLIENT.bat"
) else (
    echo ❌ Scripts de création non trouvés !
    echo    Assurez-vous d'être dans le dossier du projet CyberParc
)
goto :end

:found
echo.
echo ✅ Package localisé avec succès !
echo.
echo 📦 Contenu du package :
if exist "%DESKTOP%\CYBERPARC_CLIENT_PACKAGE" (
    dir "%DESKTOP%\CYBERPARC_CLIENT_PACKAGE" /b
) else if exist "CYBERPARC_CLIENT_PACKAGE" (
    dir "CYBERPARC_CLIENT_PACKAGE" /b
)
echo.
echo 📧 Pour envoyer à votre client :
echo    1. Utilisez l'archive ZIP si elle existe
echo    2. Ou compressez manuellement le dossier
echo    3. Joignez à votre email avec les instructions
echo.

:end
pause
