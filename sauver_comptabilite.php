<?php
// Configuration sécurisée
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Inclure la configuration
require_once 'config/database.php';

try {
  $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
  error_log("Erreur de connexion à la base de données : " . $e->getMessage());
  die("Une erreur est survenue. Veuillez réessayer plus tard.");
}

$id = $_POST['entreprise_id'];
$factures = $_POST['factures'];
$virements = $_POST['virements'];
$remarques = $_POST['remarques'];

$check = $pdo->prepare("SELECT * FROM comptabilite WHERE entreprise_id = ?");
$check->execute([$id]);

if ($check->rowCount() > 0) {
  $stmt = $pdo->prepare("UPDATE comptabilite SET factures = ?, virements = ?, remarques = ? WHERE entreprise_id = ?");
  $stmt->execute([$factures, $virements, $remarques, $id]);
} else {
  $stmt = $pdo->prepare("INSERT INTO comptabilite (entreprise_id, factures, virements, remarques) VALUES (?, ?, ?, ?)");
  $stmt->execute([$id, $factures, $virements, $remarques]);
}

header("Location: details_entreprise.php?id=$id");
exit;