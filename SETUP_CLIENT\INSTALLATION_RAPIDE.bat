@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 CYBERPARC SETUP 🚀                    ║
echo ║              Installation Automatique Rapide                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 Ce script va vous aider à installer CyberParc rapidement
echo.

pause

echo 📁 Création des dossiers nécessaires...
if not exist "uploads" mkdir uploads
if not exist "uploads\logos" mkdir uploads\logos
if not exist "uploads\recus" mkdir uploads\recus
if not exist "config" mkdir config
echo ✅ Dossiers créés avec succès
echo.

echo 📄 Copie des fichiers de configuration...
copy "SETUP_CLIENT\database.php" "config\database.php" >nul
echo ✅ Configuration copiée
echo.

echo 🔧 ÉTAPES SUIVANTES À FAIRE MANUELLEMENT :
echo.
echo 1️⃣  Ouvrez phpMyAdmin
echo 2️⃣  Créez une base de données nommée 'stage'
echo 3️⃣  Importez le fichier 'stage.sql'
echo 4️⃣  Modifiez le fichier 'config\database.php' avec vos paramètres
echo 5️⃣  Accédez à votre site web
echo.

echo 🎯 CONNEXION PAR DÉFAUT :
echo    Email : <EMAIL>
echo    Mot de passe : admin123
echo.
echo ⚠️  CHANGEZ CE MOT DE PASSE IMMÉDIATEMENT !
echo.

echo 📞 En cas de problème, consultez le fichier README.md
echo.

pause
echo.
echo ✨ Installation terminée ! Bonne utilisation !
pause
