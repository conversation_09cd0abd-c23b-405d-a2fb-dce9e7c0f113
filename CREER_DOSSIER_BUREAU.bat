@echo off
chcp 65001 >nul
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            📦 CRÉATION DOSSIER CLIENT BUREAU 📦             ║
echo ║                    CyberParc - v1.0                         ║
echo ║          Création dans un dossier dédié sur le bureau       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Définir les chemins
set "DESKTOP=%USERPROFILE%\Desktop"
set "CLIENT_FOLDER=%DESKTOP%\CYBERPARC_LIVRAISON_CLIENT"
set "PACKAGE_DIR=%CLIENT_FOLDER%\CYBERPARC_CLIENT_PACKAGE"
set "ZIP_FILE=%CLIENT_FOLDER%\CyberParc_Client_Package.zip"

echo 🎯 Création du package dans un dossier dédié sur le bureau
echo.
echo 📍 Dossier principal : %CLIENT_FOLDER%
echo 📦 Package client   : %PACKAGE_DIR%
echo 📧 Archive ZIP      : %ZIP_FILE%
echo.

:: Vérifier qu'on est dans le bon dossier
if not exist "index.php" (
    echo ❌ ERREUR : Vous n'êtes pas dans le dossier du projet CyberParc !
    echo.
    echo 💡 Naviguez vers le dossier contenant :
    echo    - index.php, accueil.php
    echo    - config/database.php
    echo    - SETUP_CLIENT/
    echo.
    pause
    exit /b 1
)

echo ✅ Dossier du projet CyberParc détecté
echo.

set /p confirm="Créer le package sur le bureau ? (O/N) : "
if /i not "%confirm%"=="O" if /i not "%confirm%"=="Y" (
    echo ❌ Opération annulée
    pause
    exit /b
)

echo.
echo 🚀 Création du dossier de livraison client...
echo.

:: Supprimer l'ancien dossier s'il existe
if exist "%CLIENT_FOLDER%" (
    echo 🗑️ Suppression de l'ancien dossier...
    rmdir /s /q "%CLIENT_FOLDER%"
)

:: Créer le dossier principal de livraison
mkdir "%CLIENT_FOLDER%"
echo ✅ Dossier de livraison créé : %CLIENT_FOLDER%

:: Créer la structure du package
echo 📁 Création de la structure du package...
mkdir "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%\config"
mkdir "%PACKAGE_DIR%\uploads"
mkdir "%PACKAGE_DIR%\uploads\logos"
mkdir "%PACKAGE_DIR%\uploads\recus"
mkdir "%PACKAGE_DIR%\vendor"
mkdir "%PACKAGE_DIR%\DOCUMENTATION"
mkdir "%PACKAGE_DIR%\OUTILS"

echo 📄 Copie des fichiers PHP principaux...
copy "*.php" "%PACKAGE_DIR%\" >nul 2>&1
copy "*.ico" "%PACKAGE_DIR%\" >nul 2>&1
copy "*.jpg" "%PACKAGE_DIR%\" >nul 2>&1
copy "composer.json" "%PACKAGE_DIR%\" >nul 2>&1
copy "composer.lock" "%PACKAGE_DIR%\" >nul 2>&1

echo 🗄️ Copie de la base de données...
copy "SETUP_CLIENT\stage.sql" "%PACKAGE_DIR%\" >nul

echo ⚙️ Copie de la configuration...
copy "SETUP_CLIENT\database.php" "%PACKAGE_DIR%\config\" >nul

echo 📚 Copie de la documentation...
copy "SETUP_CLIENT\README.md" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\GUIDE_RAPIDE.html" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\CHECKLIST.md" "%PACKAGE_DIR%\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\DOCUMENTATION_COMPLETE.md" "%PACKAGE_DIR%\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\RACCOURCIS_ADMIN.html" "%PACKAGE_DIR%\DOCUMENTATION\" >nul

echo 🛠️ Copie des outils d'installation...
copy "SETUP_CLIENT\verifier_installation.php" "%PACKAGE_DIR%\OUTILS\" >nul
copy "SETUP_CLIENT\INSTALLATION_RAPIDE.bat" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\test_final.php" "%PACKAGE_DIR%\OUTILS\" >nul

:: Copier vendor si disponible
if exist "vendor" (
    echo 📦 Copie des dépendances Composer...
    xcopy "vendor" "%PACKAGE_DIR%\vendor" /e /i /q >nul 2>&1
    echo ✅ Dépendances copiées
) else (
    echo ⚠️ Dossier vendor non trouvé - Le client devra exécuter 'composer install'
)

echo 📝 Création des fichiers d'aide...

:: Créer le fichier LISEZ-MOI principal
(
echo # 🚀 CyberParc - Système de Gestion Comptable
echo ## Version 1.0 - Package Client Professionnel
echo.
echo ### 📦 Contenu du Package
echo - **Système complet** : Tous les fichiers PHP sécurisés
echo - **Base de données** : stage.sql avec utilisateur admin
echo - **Configuration** : Template à personnaliser
echo - **Documentation** : Guides complets et outils
echo.
echo ### 🚀 Installation Express ^(5 minutes^)
echo 1. **Base de données** : Importez stage.sql dans phpMyAdmin
echo 2. **Configuration** : Modifiez config/database.php
echo 3. **Test** : Accédez à votre site
echo 4. **Connexion** : <EMAIL> / admin123
echo 5. **Sécurité** : CHANGEZ LE MOT DE PASSE IMMÉDIATEMENT !
echo.
echo ### 📚 Aide Disponible
echo - **GUIDE_RAPIDE.html** : Guide visuel complet
echo - **OUTILS/verifier_installation.php** : Diagnostic automatique
echo - **DOCUMENTATION/** : Aide technique détaillée
echo.
echo ### 🔒 Sécurité
echo ⚠️ **IMPORTANT** : Changez le mot de passe admin dès la première connexion
echo.
echo ### 📞 Support
echo Consultez la documentation incluse pour toute assistance.
echo.
echo ---
echo **CyberParc v1.0** - Système professionnel de gestion comptable
echo **Support** : Documentation complète incluse
) > "%PACKAGE_DIR%\LISEZ-MOI.md"

:: Créer le script d'installation express
(
echo @echo off
echo chcp 65001 ^>nul
echo color 0B
echo echo.
echo echo ╔══════════════════════════════════════════════════════════════╗
echo echo ║                    🚀 CYBERPARC SETUP 🚀                    ║
echo echo ║              Installation Express - 5 minutes               ║
echo echo ╚══════════════════════════════════════════════════════════════╝
echo echo.
echo echo 📋 ÉTAPES D'INSTALLATION :
echo echo.
echo echo 1️⃣  Importez 'stage.sql' dans phpMyAdmin
echo echo     ^(Créez d'abord une base de données nommée 'stage'^)
echo echo.
echo echo 2️⃣  Modifiez 'config\database.php' avec vos paramètres :
echo echo     - Serveur MySQL
echo echo     - Nom d'utilisateur
echo echo     - Mot de passe
echo echo.
echo echo 3️⃣  Accédez à votre site web
echo echo.
echo echo 4️⃣  Connectez-vous avec :
echo echo     Email : <EMAIL>
echo echo     Mot de passe : admin123
echo echo.
echo echo 5️⃣  ⚠️ CHANGEZ LE MOT DE PASSE IMMÉDIATEMENT !
echo echo.
echo echo 🔧 OUTILS DISPONIBLES :
echo echo    - GUIDE_RAPIDE.html ^(guide visuel^)
echo echo    - OUTILS\verifier_installation.php ^(diagnostic^)
echo echo    - DOCUMENTATION\ ^(aide complète^)
echo echo.
echo echo 📞 En cas de problème, consultez LISEZ-MOI.md
echo echo.
echo pause
echo echo.
echo echo ✨ Installation terminée ! Bon travail !
echo pause
) > "%PACKAGE_DIR%\INSTALLATION_EXPRESS.bat"

:: Créer un fichier de vérification rapide
(
echo ^<?php
echo // Vérification Express - CyberParc
echo echo "^<h1 style='color:#2c3e50'^>🔍 Vérification Express CyberParc^</h1^>";
echo echo "^<div style='font-family:Arial,sans-serif;max-width:600px;margin:20px'^>";
echo if ^(file_exists^('config/database.php'^)^) {
echo     echo "^<p style='color:green;font-weight:bold'^>✅ Configuration trouvée^</p^>";
echo     require_once 'config/database.php';
echo     try {
echo         $pdo = new PDO^($dsn, $username, $password, $options^);
echo         echo "^<p style='color:green;font-weight:bold'^>✅ Connexion base de données réussie^</p^>";
echo         echo "^<p style='background:#e8f5e8;padding:15px;border-radius:5px'^>";
echo         echo "^<strong^>🎉 Installation réussie !^</strong^>^<br^>";
echo         echo "^<a href='index.php' style='color:#2c3e50;font-weight:bold'^>👉 Accéder au système CyberParc^</a^>";
echo         echo "^</p^>";
echo     } catch ^(Exception $e^) {
echo         echo "^<p style='color:red;font-weight:bold'^>❌ Erreur BDD: " . htmlspecialchars^($e-^>getMessage^(^)^) . "^</p^>";
echo         echo "^<p^>Vérifiez les paramètres dans config/database.php^</p^>";
echo     }
echo } else {
echo     echo "^<p style='color:red;font-weight:bold'^>❌ Configuration manquante^</p^>";
echo     echo "^<p^>Copiez et configurez le fichier config/database.php^</p^>";
echo }
echo echo "^</div^>";
echo ?^>
) > "%PACKAGE_DIR%\test_rapide.php"

:: Créer un fichier d'informations dans le dossier de livraison
(
echo # 📦 CyberParc - Package Client
echo ## Informations de Livraison
echo.
echo **Date de création** : %DATE% %TIME%
echo **Version** : CyberParc v1.0
echo **Type** : Package client complet
echo.
echo ## 📁 Contenu
echo - **CYBERPARC_CLIENT_PACKAGE/** : Système complet à installer
echo - **CyberParc_Client_Package.zip** : Archive prête à envoyer
echo.
echo ## 📧 Instructions d'Envoi
echo 1. Envoyez l'archive ZIP à votre client
echo 2. Incluez les instructions d'installation
echo 3. Accompagnez le client si nécessaire
echo.
echo ## 🔐 Première Connexion
echo - **URL** : http://domaine-client.com
echo - **Email** : <EMAIL>  
echo - **Mot de passe** : admin123
echo - **⚠️ À changer immédiatement !**
) > "%CLIENT_FOLDER%\INFORMATIONS_LIVRAISON.md"

:: Créer l'archive ZIP si 7-Zip est disponible
where 7z >nul 2>&1
if %errorlevel% == 0 (
    echo 🗜️ Création de l'archive ZIP...
    7z a -tzip "%ZIP_FILE%" "%PACKAGE_DIR%\*" >nul
    echo ✅ Archive créée : %ZIP_FILE%
) else (
    echo ℹ️ 7-Zip non trouvé - Compression manuelle nécessaire
    echo    Vous pouvez compresser manuellement le dossier CYBERPARC_CLIENT_PACKAGE
)

echo.
echo ✅ PACKAGE CLIENT CRÉÉ AVEC SUCCÈS !
echo.
echo 📍 Emplacement : %CLIENT_FOLDER%
echo.
echo 📦 Contenu créé :
echo    📁 CYBERPARC_CLIENT_PACKAGE\ ^(système complet^)
echo    📧 CyberParc_Client_Package.zip ^(archive client^)
echo    📝 INFORMATIONS_LIVRAISON.md ^(infos^)
echo.

echo 🚀 Ouverture du dossier de livraison...
start "" "%CLIENT_FOLDER%"

echo.
echo 🎯 PRÊT POUR ENVOI CLIENT !
echo.
echo 💡 Pour envoyer à votre client :
echo    1. Utilisez l'archive ZIP créée
echo    2. Ou compressez manuellement le dossier CYBERPARC_CLIENT_PACKAGE
echo    3. Incluez les instructions d'installation
echo.

pause
