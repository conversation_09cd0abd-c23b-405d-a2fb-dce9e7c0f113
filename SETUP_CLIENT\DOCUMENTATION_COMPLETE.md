# 📖 Documentation Complète - CyberParc

## 🎯 Vue d'ensemble

CyberParc est un système de gestion comptable spécialement conçu pour les parcs technologiques et centres d'affaires. Il permet de gérer les entreprises locataires, leurs factures, les paiements et les notifications de rappel.

## 🏗️ Architecture du système

### Structure des fichiers
```
cyberparc/
├── config/
│   └── database.php          # Configuration base de données
├── uploads/
│   ├── logos/               # Logos des entreprises
│   └── recus/               # Reçus de paiement
├── SETUP_CLIENT/            # Dossier d'installation
├── index.php                # Page de connexion
├── accueil.php             # Tableau de bord principal
├── ajouter_entreprise.php   # Ajout d'entreprises
├── ajouter_facture.php      # Création de factures
├── ajouter_virement.php     # Enregistrement des paiements
├── notifications.php        # Gestion des alertes
└── ...
```

### Base de données
- **utilisateurs** : Comptes d'accès au système
- **entreprises** : Informations des entreprises locataires
- **facture** : Factures émises
- **virement** : Paiements reçus
- **notifications** : Alertes et rappels
- **alertes_paiement** : Historique des relances

## 🚀 Guide d'utilisation

### Première connexion
1. Accédez à votre site
2. Connectez-vous avec : <EMAIL> / admin123
3. **Changez immédiatement le mot de passe**

### Gestion des entreprises
1. **Ajouter une entreprise** :
   - Nom, responsable, email, téléphone
   - Logo (optionnel)
   - Local d'affectation

2. **Modifier une entreprise** :
   - Cliquez sur "Modifier" dans la liste
   - Mettez à jour les informations
   - Sauvegardez

3. **Activer/Désactiver** :
   - Utilisez le bouton de statut
   - Les entreprises inactives n'apparaissent plus dans les rapports

### Facturation
1. **Créer une facture** :
   - Sélectionnez l'entreprise
   - Montant et période
   - Date d'échéance
   - Statut de paiement

2. **Modifier une facture** :
   - Changez le montant ou les dates
   - Mettez à jour le statut de paiement

### Paiements
1. **Enregistrer un virement** :
   - Sélectionnez l'entreprise
   - Montant reçu
   - Date de réception
   - Référence (optionnel)

### Notifications
- **Automatiques** : Le système génère des alertes pour les impayés
- **Manuelles** : Marquez les notifications comme lues
- **Suppression** : Supprimez les notifications obsolètes

## 🔧 Configuration avancée

### Paramètres de base de données
```php
// config/database.php
$host = 'localhost';
$dbname = 'stage';
$username = 'votre_user';
$password = 'votre_mdp';
```

### Configuration email
```php
$email_config = [
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'mot-de-passe-app',
    'from_email' => '<EMAIL>',
    'from_name' => 'Votre Entreprise'
];
```

### Permissions des dossiers
```bash
chmod 755 uploads/
chmod 755 uploads/logos/
chmod 755 uploads/recus/
```

## 🛠️ Maintenance

### Sauvegardes
1. **Base de données** : Exportez régulièrement via phpMyAdmin
2. **Fichiers** : Sauvegardez le dossier uploads/
3. **Configuration** : Gardez une copie de config/database.php

### Mises à jour
1. Sauvegardez avant toute mise à jour
2. Testez sur un environnement de développement
3. Vérifiez la compatibilité des versions PHP/MySQL

### Logs d'erreur
- Consultez les logs du serveur web
- Activez temporairement les erreurs PHP pour le debug
- Surveillez l'espace disque

## 🔒 Sécurité

### Bonnes pratiques
1. **Mots de passe** :
   - Minimum 8 caractères
   - Mélange majuscules/minuscules/chiffres
   - Changement régulier

2. **Accès** :
   - Limitez les comptes utilisateurs
   - Supprimez les comptes inutilisés
   - Surveillez les connexions

3. **Serveur** :
   - Mettez à jour PHP/MySQL
   - Configurez un firewall
   - Utilisez HTTPS

### Sauvegarde de sécurité
```sql
-- Sauvegarde complète
mysqldump -u username -p stage > backup_$(date +%Y%m%d).sql

-- Restauration
mysql -u username -p stage < backup_20250101.sql
```

## 📊 Rapports et statistiques

### Données disponibles
- Nombre d'entreprises par statut
- Montant total des factures
- Soldes impayés par entreprise
- Historique des paiements

### Export Excel
- Utilisez la fonction "Sauver comptabilité"
- Données exportées : entreprises, factures, virements
- Format compatible Excel/LibreOffice

## 🆘 Dépannage

### Problèmes courants

**Page blanche** :
- Vérifiez les logs d'erreur
- Contrôlez les permissions des fichiers
- Vérifiez la configuration PHP

**Erreur de base de données** :
- Vérifiez config/database.php
- Testez la connexion MySQL
- Contrôlez les permissions utilisateur

**Upload impossible** :
- Vérifiez les permissions uploads/
- Contrôlez upload_max_filesize dans PHP
- Vérifiez l'espace disque

**Emails non envoyés** :
- Vérifiez la configuration SMTP
- Testez avec un autre serveur email
- Contrôlez les ports réseau

### Contacts support
- Documentation : README.md
- Checklist : CHECKLIST.md
- Vérification : verifier_installation.php

## 📈 Évolutions futures

### Fonctionnalités prévues
- Interface mobile responsive
- API REST pour intégrations
- Rapports avancés avec graphiques
- Gestion multi-utilisateurs
- Notifications SMS

### Personnalisations possibles
- Thèmes et couleurs
- Champs personnalisés
- Workflows de validation
- Intégration comptable

---

**Version** : CyberParc v1.0  
**Dernière mise à jour** : Janvier 2025  
**Support** : Consultez la documentation ou contactez votre développeur
