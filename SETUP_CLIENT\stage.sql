-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3306
-- <PERSON><PERSON><PERSON><PERSON> le : jeu. 31 juil. 2025 à 11:15
-- Version du serveur : 9.1.0
-- Version de PHP : 8.3.14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `stage`
--

-- --------------------------------------------------------

--
-- Structure de la table `alertes_paiement`
--

DROP TABLE IF EXISTS `alertes_paiement`;
CREATE TABLE IF NOT EXISTS `alertes_paiement` (
  `id` int NOT NULL AUTO_INCREMENT,
  `entreprise_id` int NOT NULL,
  `facture_id` int NOT NULL,
  `type_alerte` enum('rappel','relance_1','relance_2','mise_en_demeure') COLLATE utf8mb4_general_ci NOT NULL,
  `date_envoi` datetime NOT NULL,
  `email_envoye` tinyint(1) DEFAULT '0',
  `date_creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_alerte` (`entreprise_id`,`facture_id`,`type_alerte`),
  KEY `facture_id` (`facture_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `companies`
--

DROP TABLE IF EXISTS `companies`;
CREATE TABLE IF NOT EXISTS `companies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `companies`
--

INSERT INTO `companies` (`id`, `nom`, `created_at`, `updated_at`) VALUES
(1, 'Tech Solutions Inc.', '2025-07-17 09:33:48', '2025-07-17 09:33:48'),
(2, 'Global Innovations Ltd.', '2025-07-17 09:33:49', '2025-07-17 09:33:49'),
(3, 'Future Systems Co.', '2025-07-17 09:33:49', '2025-07-17 09:33:49'),
(4, 'Tech Solutions Inc.', '2025-07-17 09:40:46', '2025-07-17 09:40:46'),
(5, 'Global Innovations Ltd.', '2025-07-17 09:40:46', '2025-07-17 09:40:46'),
(6, 'Future Systems Co.', '2025-07-17 09:40:46', '2025-07-17 09:40:46');

-- --------------------------------------------------------

--
-- Structure de la table `entreprises`
--

DROP TABLE IF EXISTS `entreprises`;
CREATE TABLE IF NOT EXISTS `entreprises` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `chef` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `numero` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(150) COLLATE utf8mb4_general_ci NOT NULL,
  `logo_url` text COLLATE utf8mb4_general_ci,
  `local` varchar(150) COLLATE utf8mb4_general_ci NOT NULL,
  `date_creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `logo` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_entreprises_logo` (`logo`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `entreprises`
--

INSERT INTO `entreprises` (`id`, `nom`, `chef`, `numero`, `email`, `logo_url`, `local`, `date_creation`, `active`, `logo`, `updated_at`) VALUES
(12, 'online', 'souad', '98562569', '<EMAIL>', NULL, 'Djerba', '2025-07-18 09:53:46', 1, '687a19aacac33.jpg', '2025-07-30 09:35:10'),
(13, 'doussa', 'ferdaous', '98562569', '<EMAIL>', NULL, 'Djerba', '2025-07-30 08:08:14', 1, '6889d2ee21935.jpeg', '2025-07-30 09:08:48'),
(14, 'pointeur', 'ahmed', '24790784', '<EMAIL>', NULL, 'Djerba', '2025-07-30 08:18:55', 1, '6889d56f312db.jpg', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `facture`
--

DROP TABLE IF EXISTS `facture`;
CREATE TABLE IF NOT EXISTS `facture` (
  `id` int NOT NULL AUTO_INCREMENT,
  `numFact` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `dateEmission` date NOT NULL,
  `date_echeance` date DEFAULT NULL,
  `Montant` decimal(10,2) NOT NULL,
  `entreprise_id` int NOT NULL,
  `reference` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `periode` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `dateFin` date DEFAULT NULL,
  `dateDebut` date DEFAULT NULL,
  `statut_paiement` varchar(50) COLLATE utf8mb4_general_ci DEFAULT 'non payé',
  PRIMARY KEY (`id`),
  KEY `entreprise_id` (`entreprise_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `facture`
--

INSERT INTO `facture` (`id`, `numFact`, `dateEmission`, `date_echeance`, `Montant`, `entreprise_id`, `reference`, `periode`, `dateFin`, `dateDebut`, `statut_paiement`) VALUES
(18, '1', '2024-05-12', NULL, 1200.00, 12, 'ref:1', '', '2025-05-12', '2024-05-12', 'non payé'),
(19, '1', '2025-09-30', NULL, 1200.00, 13, 'ref:1', '', '2025-05-13', '2024-04-13', 'non payé'),
(20, '2', '2025-07-30', NULL, 200.00, 13, 'ref:2', '', '2025-09-12', '2022-05-12', 'non payé'),
(21, '1865', '2025-07-30', NULL, 1200.00, 14, '1865', '', '2025-09-01', '2025-07-01', 'non payé'),
(22, '13', '2025-07-31', NULL, 1200.00, 12, '12', '', '2025-05-15', '2024-05-15', 'non payé'),
(23, '5555', '2025-07-31', NULL, 1200.00, 12, '55555', '', '2024-05-12', '2023-05-12', 'non payé');

-- --------------------------------------------------------

--
-- Structure de la table `factures`
--

DROP TABLE IF EXISTS `factures`;
CREATE TABLE IF NOT EXISTS `factures` (
  `id` int NOT NULL AUTO_INCREMENT,
  `numero_facture` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `montant` decimal(10,2) NOT NULL,
  `date_emission` date NOT NULL,
  `date_limite` date NOT NULL,
  `statut_paiement` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'non payé',
  `client_id` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `numero_facture` (`numero_facture`),
  KEY `fk_company_invoice` (`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `utilisateur_id` int NOT NULL,
  `entreprise_id` int DEFAULT NULL,
  `type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `message` text COLLATE utf8mb4_general_ci,
  `vu` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `company_id` int DEFAULT NULL,
  `facture_id` int DEFAULT NULL,
  `date_creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_company_id` (`company_id`),
  KEY `fk_facture_id` (`facture_id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `notifications`
--

INSERT INTO `notifications` (`id`, `utilisateur_id`, `entreprise_id`, `type`, `message`, `vu`, `created_at`, `company_id`, `facture_id`, `date_creation`, `timestamp`) VALUES
(50, 18, 12, 'facture_trimestre_impayee', '???? ALERTE : online a un solde impayé de 2 400,00 DT. La plus ancienne facture date de 12/05/2024 (il y a 14.6 mois).', 0, '2025-07-31 11:35:08', NULL, NULL, '2025-07-31 10:35:08', '2025-07-31 10:35:08');

-- --------------------------------------------------------

--
-- Structure de la table `password_reset`
--

DROP TABLE IF EXISTS `password_reset`;
CREATE TABLE IF NOT EXISTS `password_reset` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `code` varchar(6) COLLATE utf8mb4_general_ci NOT NULL,
  `expiration` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  KEY `idx_email_code` (`email`,`code`),
  KEY `idx_expiration` (`expiration`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `password_reset`
--

INSERT INTO `password_reset` (`id`, `email`, `code`, `expiration`, `created_at`) VALUES
(1, '<EMAIL>', '819364', '2025-07-02 22:25:48', '2025-07-02 19:25:48');

-- --------------------------------------------------------

--
-- Structure de la table `utilisateurs`
--

DROP TABLE IF EXISTS `utilisateurs`;
CREATE TABLE IF NOT EXISTS `utilisateurs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(150) COLLATE utf8mb4_general_ci NOT NULL,
  `motdepasse` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `telephone` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `gouvernorat` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `local` varchar(150) COLLATE utf8mb4_general_ci NOT NULL,
  `date_inscription` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reset_token` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reset_token_expire` datetime DEFAULT NULL,
  `reset_expire` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `utilisateurs`
--

INSERT INTO `utilisateurs` (`id`, `nom`, `email`, `motdepasse`, `telephone`, `gouvernorat`, `local`, `date_inscription`, `reset_token`, `reset_token_expire`, `reset_expire`) VALUES
(1, 'Administrateur', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '00000000', 'Tunis', 'Siège', NOW(), NULL, NULL, NULL),
(18, 'ferdaous ben moussa', '<EMAIL>', '$2y$10$WSxELxsyccMh2/0c.H8CmOOtPDUJYXcNR3vrMcVqWhevRewBFfgUK', '24790784', 'Médenine', 'Djerba', '2025-07-18 09:52:58', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `virement`
--

DROP TABLE IF EXISTS `virement`;
CREATE TABLE IF NOT EXISTS `virement` (
  `id` int NOT NULL AUTO_INCREMENT,
  `numVirem` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `typePai` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `Montant` decimal(10,2) NOT NULL,
  `entreprise_id` int NOT NULL,
  `date_virement` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fichierRecu` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `entreprise_id` (`entreprise_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `virement`
--

INSERT INTO `virement` (`id`, `numVirem`, `typePai`, `Montant`, `entreprise_id`, `date_virement`, `fichierRecu`) VALUES
(22, '1', 'Espèces', 1100.00, 12, '2025-07-18 09:56:15', 'recu_12_1752832575_687a1a3f2fb37.png'),
(23, '1', 'Chèque', 1200.00, 13, '2025-07-30 08:12:23', 'recu_13_1753863143_6889d3e73fde4.jpeg'),
(24, '1', 'Chèque', 500.00, 14, '2025-07-30 08:21:14', 'recu_14_1753863674_6889d5fac32b9.png'),
(25, '12', 'Espèces', 100.00, 12, '2025-07-30 08:22:50', 'recu_12_1753863770_6889d65ab46b5.png');

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `alertes_paiement`
--
ALTER TABLE `alertes_paiement`
  ADD CONSTRAINT `alertes_paiement_ibfk_1` FOREIGN KEY (`entreprise_id`) REFERENCES `entreprises` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `alertes_paiement_ibfk_2` FOREIGN KEY (`facture_id`) REFERENCES `facture` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `facture`
--
ALTER TABLE `facture`
  ADD CONSTRAINT `facture_ibfk_1` FOREIGN KEY (`entreprise_id`) REFERENCES `entreprises` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `factures`
--
ALTER TABLE `factures`
  ADD CONSTRAINT `fk_company_invoice` FOREIGN KEY (`client_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL;

--
-- Contraintes pour la table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `fk_company_id` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_facture_id` FOREIGN KEY (`facture_id`) REFERENCES `factures` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Contraintes pour la table `virement`
--
ALTER TABLE `virement`
  ADD CONSTRAINT `virement_ibfk_1` FOREIGN KEY (`entreprise_id`) REFERENCES `entreprises` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
