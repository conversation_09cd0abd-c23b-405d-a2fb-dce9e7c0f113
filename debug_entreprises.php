<?php
session_start();

// Activer l'affichage des erreurs pour le debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug - Entreprises</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }
    .debug-section { background: white; padding: 1rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .debug-title { color: #1e3c72; font-size: 1.2rem; margin-bottom: 1rem; }
    .success { color: #16a34a; background: #f0fdf4; padding: 0.5rem; border-radius: 4px; }
    .error { color: #dc2626; background: #fef2f2; padding: 0.5rem; border-radius: 4px; }
    .info { color: #1e90ff; background: #eff6ff; padding: 0.5rem; border-radius: 4px; }
    table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
    th, td { padding: 0.5rem; border: 1px solid #e5e7eb; text-align: left; }
    th { background: #f9fafb; font-weight: 600; }
    .logo-preview { width: 50px; height: 50px; object-fit: cover; border-radius: 4px; }
</style>";

try {
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>✅ Connexion à la base de données</div>";
  echo "<div class='success'>Connexion réussie à la base de données 'stage'</div>";
  echo "</div>";

  // Vérifier la session
  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>👤 Informations de session</div>";
  if (isset($_SESSION['utilisateur'])) {
    echo "<div class='success'>Utilisateur connecté : " . htmlspecialchars($_SESSION['utilisateur']['nom'] ?? 'Non défini') . "</div>";
    echo "<div class='info'>Local : " . htmlspecialchars($_SESSION['utilisateur']['local'] ?? 'Non défini') . "</div>";
    $local = $_SESSION['utilisateur']['local'] ?? '';
  } else {
    echo "<div class='error'>Aucune session utilisateur trouvée</div>";
    $local = '';
  }
  echo "</div>";

  // Vérifier la structure de la table entreprises
  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>🗃️ Structure de la table entreprises</div>";
  $columns = $pdo->query("SHOW COLUMNS FROM entreprises")->fetchAll(PDO::FETCH_ASSOC);
  echo "<table>";
  echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Défaut</th></tr>";
  foreach ($columns as $col) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
    echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
    echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
    echo "<td>" . htmlspecialchars($col['Default'] ?? 'NULL') . "</td>";
    echo "</tr>";
  }
  echo "</table>";

  $hasLogoColumn = false;
  foreach ($columns as $col) {
    if ($col['Field'] === 'logo') {
      $hasLogoColumn = true;
      break;
    }
  }

  if ($hasLogoColumn) {
    echo "<div class='success'>✅ Colonne 'logo' présente</div>";
  } else {
    echo "<div class='error'>❌ Colonne 'logo' manquante</div>";
    echo "<div class='info'>Pour ajouter la colonne logo : ALTER TABLE entreprises ADD COLUMN logo VARCHAR(255) NULL;</div>";
  }
  echo "</div>";

  // Compter toutes les entreprises
  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>📊 Statistiques générales</div>";
  $totalEntreprises = $pdo->query("SELECT COUNT(*) FROM entreprises")->fetchColumn();
  echo "<div class='info'>Total des entreprises : " . $totalEntreprises . "</div>";

  if (!empty($local)) {
    $entreprisesLocal = $pdo->prepare("SELECT COUNT(*) FROM entreprises WHERE local = ?");
    $entreprisesLocal->execute([$local]);
    $countLocal = $entreprisesLocal->fetchColumn();
    echo "<div class='info'>Entreprises dans votre local ('{$local}') : " . $countLocal . "</div>";
  }

  $entreprisesActives = $pdo->query("SELECT COUNT(*) FROM entreprises WHERE active = 1")->fetchColumn();
  $entreprisesInactives = $pdo->query("SELECT COUNT(*) FROM entreprises WHERE active = 0")->fetchColumn();
  echo "<div class='success'>Entreprises actives : " . $entreprisesActives . "</div>";
  echo "<div class='error'>Entreprises inactives : " . $entreprisesInactives . "</div>";
  echo "</div>";

  // Lister toutes les entreprises
  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>📋 Liste complète des entreprises</div>";
  $entreprises = $pdo->query("SELECT * FROM entreprises ORDER BY id DESC")->fetchAll(PDO::FETCH_ASSOC);

  if (empty($entreprises)) {
    echo "<div class='error'>Aucune entreprise trouvée dans la base de données</div>";
  } else {
    echo "<table>";
    echo "<tr><th>ID</th><th>Logo</th><th>Nom</th><th>Chef</th><th>Email</th><th>Local</th><th>Actif</th><th>Créé le</th></tr>";
    foreach ($entreprises as $e) {
      $activeClass = ($e['active'] == 1) ? 'success' : 'error';
      echo "<tr>";
      echo "<td>" . $e['id'] . "</td>";
      echo "<td>";
      if (!empty($e['logo']) && file_exists('uploads/logos/' . $e['logo'])) {
        echo "<img src='uploads/logos/" . htmlspecialchars($e['logo']) . "' class='logo-preview' alt='Logo'>";
      } else {
        echo "🏢";
      }
      echo "</td>";
      echo "<td>" . htmlspecialchars($e['nom']) . "</td>";
      echo "<td>" . htmlspecialchars($e['chef']) . "</td>";
      echo "<td>" . htmlspecialchars($e['email']) . "</td>";
      echo "<td>" . htmlspecialchars($e['local'] ?? 'Non défini') . "</td>";
      echo "<td class='{$activeClass}'>" . ($e['active'] == 1 ? '✅ Oui' : '❌ Non') . "</td>";
      echo "<td>" . ($e['created_at'] ?? 'Non défini') . "</td>";
      echo "</tr>";
    }
    echo "</table>";
  }
  echo "</div>";

  // Vérifier les dossiers d'upload
  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>📁 Vérification des dossiers d'upload</div>";

  $uploadDirs = [
    'uploads/' => 'Dossier principal uploads',
    'uploads/logos/' => 'Dossier des logos',
    'uploads/recus/' => 'Dossier des reçus'
  ];

  foreach ($uploadDirs as $dir => $description) {
    if (file_exists($dir)) {
      $files = glob($dir . '*');
      $fileCount = count($files);
      echo "<div class='success'>✅ {$description} : existe ({$fileCount} fichiers)</div>";

      if ($dir === 'uploads/logos/' && $fileCount > 0) {
        echo "<div class='info'>Logos trouvés : " . implode(', ', array_map('basename', $files)) . "</div>";
      }
    } else {
      echo "<div class='error'>❌ {$description} : n'existe pas</div>";
      echo "<div class='info'>Création automatique lors du prochain upload</div>";
    }
  }
  echo "</div>";

  // Tester une requête avec filtre local
  if (!empty($local)) {
    echo "<div class='debug-section'>";
    echo "<div class='debug-title'>🔍 Test de la requête avec filtre local</div>";
    $stmt = $pdo->prepare("SELECT * FROM entreprises WHERE local = ? ORDER BY active DESC, nom ASC");
    $stmt->execute([$local]);
    $entreprisesFiltered = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<div class='info'>Requête exécutée : SELECT * FROM entreprises WHERE local = '{$local}' ORDER BY active DESC, nom ASC</div>";
    echo "<div class='info'>Résultats trouvés : " . count($entreprisesFiltered) . "</div>";

    if (!empty($entreprisesFiltered)) {
      echo "<table>";
      echo "<tr><th>ID</th><th>Nom</th><th>Chef</th><th>Email</th><th>Actif</th></tr>";
      foreach ($entreprisesFiltered as $e) {
        $activeClass = ($e['active'] == 1) ? 'success' : 'error';
        echo "<tr>";
        echo "<td>" . $e['id'] . "</td>";
        echo "<td>" . htmlspecialchars($e['nom']) . "</td>";
        echo "<td>" . htmlspecialchars($e['chef']) . "</td>";
        echo "<td>" . htmlspecialchars($e['email']) . "</td>";
        echo "<td class='{$activeClass}'>" . ($e['active'] == 1 ? '✅ Oui' : '❌ Non') . "</td>";
        echo "</tr>";
      }
      echo "</table>";
    }
    echo "</div>";
  }
} catch (PDOException $e) {
  echo "<div class='debug-section'>";
  echo "<div class='debug-title'>❌ Erreur de base de données</div>";
  echo "<div class='error'>Erreur : " . htmlspecialchars($e->getMessage()) . "</div>";
  echo "</div>";
}

echo "<div class='debug-section'>";
echo "<div class='debug-title'>🔧 Actions recommandées</div>";
echo "<div class='info'>1. Vérifiez que vous êtes bien connecté avec un utilisateur ayant un 'local' défini</div>";
echo "<div class='info'>2. Assurez-vous que les entreprises ont bien le même 'local' que votre utilisateur</div>";
echo "<div class='info'>3. Vérifiez les permissions des dossiers uploads (755)</div>";
echo "<div class='info'>4. Testez l'ajout d'une nouvelle entreprise via le formulaire</div>";
echo "</div>";

echo "<div class='debug-section'>";
echo "<a href='accueil.php' style='background: #1e90ff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 4px;'>← Retour à l'accueil</a>";
echo " <a href='ajouter_entreprise.php' style='background: #10b981; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 4px; margin-left: 1rem;'>➕ Ajouter une entreprise</a>";
echo "</div>";