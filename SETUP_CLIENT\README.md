# 🚀 Installation Rapide - CyberParcApp

## 📋 Prérequis

Avant de commencer, assurez-vous d'avoir :
- Un serveur web (Apache/Nginx) avec PHP 7.4+
- MySQL/MariaDB
- Un nom de domaine ou accès à votre serveur

## 🔧 Installation Automatique

### Étape 1 : Téléchargement
1. Téléchargez tous les fichiers de ce dossier
2. Décompressez-les sur votre serveur web

### Étape 2 : Configuration de la base de données
1. Ouvrez phpMyAdmin ou votre interface de gestion MySQL
2. Créez une nouvelle base de données nommée `stage`
3. Importez le fichier `stage.sql` dans cette base de données

### Étape 3 : Configuration du système
1. Ouvrez le fichier `config/database.php`
2. Modifiez les paramètres de connexion :
   ```php
   $host = 'localhost';        // Votre serveur MySQL
   $dbname = 'stage';          // Nom de votre base de données
   $username = 'votre_user';   // Votre utilisateur MySQL
   $password = 'votre_mdp';    // Votre mot de passe MySQL
   ```

### Étape 4 : Configuration Email (optionnel)
Pour la récupération de mot de passe, modifiez dans `config/database.php` :
```php
$email_config = [
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'votre-mot-de-passe-app',
    'from_email' => '<EMAIL>',
    'from_name' => 'Votre Entreprise'
];
```

### Étape 5 : Permissions
Assurez-vous que les dossiers suivants ont les permissions d'écriture (755) :
- `uploads/`
- `uploads/logos/`
- `uploads/recus/`

## 🎯 Premier Démarrage

1. Accédez à votre site : `http://votre-domaine.com`
2. Connectez-vous avec le compte administrateur par défaut :
   - **Email** : <EMAIL>
   - **Mot de passe** : admin123

⚠️ **IMPORTANT** : Changez immédiatement ce mot de passe après la première connexion !

## 📞 Support

En cas de problème :
1. Vérifiez les logs d'erreur de votre serveur
2. Assurez-vous que PHP et MySQL sont correctement configurés
3. Contactez votre développeur si nécessaire

## 🔒 Sécurité

- Changez tous les mots de passe par défaut
- Configurez des sauvegardes régulières
- Mettez à jour régulièrement le système
