# ✅ Checklist d'Installation - CyberParc

## 📋 Avant l'installation

- [ ] Serveur web avec PHP 7.4+ disponible
- [ ] MySQL/MariaDB installé et accessible
- [ ] Accès à phpMyAdmin ou interface similaire
- [ ] Tous les fichiers CyberParc téléchargés

## 🔧 Installation

### Base de données
- [ ] Base de données `stage` créée
- [ ] Fichier `stage.sql` importé avec succès
- [ ] Tables créées (utilisateurs, entreprises, factures, etc.)

### Fichiers
- [ ] Tous les fichiers PHP copiés sur le serveur
- [ ] Dossier `config/` créé
- [ ] Fichier `config/database.php` configuré
- [ ] Dossiers `uploads/`, `uploads/logos/`, `uploads/recus/` créés
- [ ] Permissions d'écriture accordées aux dossiers uploads

### Configuration
- [ ] Paramètres de base de données modifiés dans `config/database.php`
- [ ] Configuration email mise à jour (optionnel)
- [ ] Test de connexion à la base de données réussi

## 🎯 Premier démarrage

- [ ] Site accessible via navigateur
- [ ] Page de connexion s'affiche correctement
- [ ] <NAME_EMAIL> / admin123 réussie
- [ ] Interface d'administration accessible

## 🔒 Sécurité post-installation

- [ ] Mot de passe administrateur changé
- [ ] Nouveau compte utilisateur créé
- [ ] Compte admin par défaut supprimé ou désactivé
- [ ] Configuration email sécurisée (si utilisée)

## 🧪 Tests fonctionnels

- [ ] Ajout d'une entreprise test
- [ ] Upload d'un logo d'entreprise
- [ ] Création d'une facture test
- [ ] Ajout d'un virement test
- [ ] Génération d'une notification
- [ ] Sauvegarde de comptabilité

## 📞 En cas de problème

### Erreurs courantes et solutions

**Erreur de connexion à la base de données :**
- Vérifier les paramètres dans `config/database.php`
- S'assurer que MySQL est démarré
- Vérifier les permissions utilisateur MySQL

**Page blanche ou erreur 500 :**
- Vérifier les logs d'erreur du serveur
- S'assurer que PHP est correctement configuré
- Vérifier les permissions des fichiers

**Impossible d'uploader des fichiers :**
- Vérifier les permissions des dossiers uploads
- Vérifier la configuration PHP (upload_max_filesize)

**Emails non envoyés :**
- Vérifier la configuration SMTP dans `config/database.php`
- S'assurer que les ports SMTP ne sont pas bloqués
- Utiliser un mot de passe d'application Gmail

## 📝 Notes importantes

- Gardez une sauvegarde de votre base de données
- Mettez à jour régulièrement le système
- Surveillez les logs d'erreur
- Documentez vos modifications personnalisées

---

**✨ Installation terminée avec succès !**

Date d'installation : _______________
Installé par : _______________
Version : CyberParc v1.0
