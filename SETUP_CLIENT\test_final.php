<?php
/**
 * Test final de vérification - CyberParc
 * Ce script teste toutes les fonctionnalités principales
 */

// Configuration
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - CyberParc</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
        h1 { text-align: center; color: #333; }
        h2 { color: #555; border-bottom: 2px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
        .test-title { font-weight: bold; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Final - CyberParc</h1>
        
        <h2>📋 Tests de Configuration</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">Configuration Base de Données</div>
                <?php
                if (file_exists('../config/database.php')) {
                    echo "<div class='success'>✅ Fichier config/database.php trouvé</div>";
                    
                    try {
                        require_once '../config/database.php';
                        echo "<div class='success'>✅ Configuration chargée</div>";
                        
                        $pdo = new PDO($dsn, $username, $password, $options);
                        echo "<div class='success'>✅ Connexion base de données réussie</div>";
                        
                        // Test des tables
                        $tables = ['utilisateurs', 'entreprises', 'facture', 'virement', 'notifications'];
                        foreach ($tables as $table) {
                            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                            if ($stmt->rowCount() > 0) {
                                echo "<div class='success'>✅ Table $table présente</div>";
                            } else {
                                echo "<div class='error'>❌ Table $table manquante</div>";
                            }
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ Erreur: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Fichier config/database.php manquant</div>";
                }
                ?>
            </div>

            <div class="test-card">
                <div class="test-title">Fichiers Principaux</div>
                <?php
                $files = [
                    '../index.php' => 'Page de connexion',
                    '../accueil.php' => 'Tableau de bord',
                    '../ajouter_entreprise.php' => 'Ajout entreprise',
                    '../ajouter_facture.php' => 'Ajout facture',
                    '../notifications.php' => 'Notifications'
                ];
                
                foreach ($files as $file => $desc) {
                    if (file_exists($file)) {
                        echo "<div class='success'>✅ $desc</div>";
                    } else {
                        echo "<div class='error'>❌ $desc manquant</div>";
                    }
                }
                ?>
            </div>

            <div class="test-card">
                <div class="test-title">Dossiers et Permissions</div>
                <?php
                $dirs = [
                    '../uploads' => 'Dossier uploads',
                    '../uploads/logos' => 'Dossier logos',
                    '../uploads/recus' => 'Dossier reçus'
                ];
                
                foreach ($dirs as $dir => $desc) {
                    if (is_dir($dir)) {
                        if (is_writable($dir)) {
                            echo "<div class='success'>✅ $desc (accessible en écriture)</div>";
                        } else {
                            echo "<div class='warning'>⚠️ $desc (pas d'écriture)</div>";
                        }
                    } else {
                        echo "<div class='info'>ℹ️ $desc sera créé automatiquement</div>";
                    }
                }
                ?>
            </div>

            <div class="test-card">
                <div class="test-title">Extensions PHP</div>
                <?php
                $extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'gd'];
                foreach ($extensions as $ext) {
                    if (extension_loaded($ext)) {
                        echo "<div class='success'>✅ Extension $ext</div>";
                    } else {
                        echo "<div class='error'>❌ Extension $ext manquante</div>";
                    }
                }
                ?>
            </div>
        </div>

        <h2>🔍 Tests Fonctionnels</h2>
        <div class="test-grid">
            <div class="test-card">
                <div class="test-title">Test Utilisateur Admin</div>
                <?php
                if (isset($pdo)) {
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateurs WHERE email = '<EMAIL>'");
                        $count = $stmt->fetchColumn();
                        
                        if ($count > 0) {
                            echo "<div class='success'>✅ Compte admin trouvé</div>";
                            
                            $stmt = $pdo->query("SELECT nom, local FROM utilisateurs WHERE email = '<EMAIL>'");
                            $admin = $stmt->fetch();
                            echo "<div class='info'>ℹ️ Nom: " . htmlspecialchars($admin['nom']) . "</div>";
                            echo "<div class='info'>ℹ️ Local: " . htmlspecialchars($admin['local']) . "</div>";
                        } else {
                            echo "<div class='error'>❌ Compte admin non trouvé</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ Erreur: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                }
                ?>
            </div>

            <div class="test-card">
                <div class="test-title">Test Données Exemple</div>
                <?php
                if (isset($pdo)) {
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM entreprises");
                        $entreprises = $stmt->fetchColumn();
                        echo "<div class='info'>ℹ️ $entreprises entreprise(s) en base</div>";
                        
                        $stmt = $pdo->query("SELECT COUNT(*) FROM facture");
                        $factures = $stmt->fetchColumn();
                        echo "<div class='info'>ℹ️ $factures facture(s) en base</div>";
                        
                        $stmt = $pdo->query("SELECT COUNT(*) FROM virement");
                        $virements = $stmt->fetchColumn();
                        echo "<div class='info'>ℹ️ $virements virement(s) en base</div>";
                        
                        if ($entreprises > 0 || $factures > 0 || $virements > 0) {
                            echo "<div class='success'>✅ Données de test présentes</div>";
                        } else {
                            echo "<div class='warning'>⚠️ Aucune donnée de test</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ Erreur: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                }
                ?>
            </div>

            <div class="test-card">
                <div class="test-title">Test Configuration Email</div>
                <?php
                if (isset($email_config)) {
                    if ($email_config['smtp_username'] !== '<EMAIL>') {
                        echo "<div class='success'>✅ Configuration email personnalisée</div>";
                        echo "<div class='info'>ℹ️ SMTP: " . htmlspecialchars($email_config['smtp_host']) . "</div>";
                    } else {
                        echo "<div class='warning'>⚠️ Configuration email par défaut</div>";
                        echo "<div class='info'>ℹ️ Récupération de mot de passe non disponible</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Configuration email non trouvée</div>";
                }
                ?>
            </div>

            <div class="test-card">
                <div class="test-title">Test Sécurité</div>
                <?php
                // Vérifier que les erreurs sont désactivées en production
                if (ini_get('display_errors')) {
                    echo "<div class='warning'>⚠️ Affichage des erreurs activé (mode debug)</div>";
                } else {
                    echo "<div class='success'>✅ Affichage des erreurs désactivé</div>";
                }
                
                // Vérifier la version PHP
                $phpVersion = phpversion();
                if (version_compare($phpVersion, '7.4.0', '>=')) {
                    echo "<div class='success'>✅ PHP $phpVersion (compatible)</div>";
                } else {
                    echo "<div class='error'>❌ PHP $phpVersion (version trop ancienne)</div>";
                }
                
                // Vérifier les permissions
                if (function_exists('password_hash')) {
                    echo "<div class='success'>✅ Fonctions de hachage disponibles</div>";
                } else {
                    echo "<div class='error'>❌ Fonctions de hachage manquantes</div>";
                }
                ?>
            </div>
        </div>

        <h2>🎯 Résumé Final</h2>
        <?php
        $errors = substr_count(ob_get_contents(), 'class=\'error\'');
        $warnings = substr_count(ob_get_contents(), 'class=\'warning\'');
        
        if ($errors == 0) {
            if ($warnings == 0) {
                echo "<div class='success'><h3>🎉 PARFAIT ! Votre installation CyberParc est complète et fonctionnelle.</h3>";
                echo "<p><strong>Prochaines étapes :</strong></p>";
                echo "<ul>";
                echo "<li>Accédez à <a href='../index.php'>votre site</a></li>";
                echo "<li>Connectez-vous avec : <EMAIL> / admin123</li>";
                echo "<li><strong>Changez immédiatement le mot de passe administrateur</strong></li>";
                echo "<li>Supprimez ce fichier de test pour la sécurité</li>";
                echo "</ul></div>";
            } else {
                echo "<div class='warning'><h3>⚠️ Installation fonctionnelle avec $warnings avertissement(s)</h3>";
                echo "<p>Le système peut être utilisé mais certaines fonctionnalités peuvent être limitées.</p></div>";
            }
        } else {
            echo "<div class='error'><h3>❌ Installation incomplète : $errors erreur(s) détectée(s)</h3>";
            echo "<p>Veuillez corriger ces problèmes avant d'utiliser le système.</p></div>";
        }
        ?>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🔒 Sécurité :</strong> Supprimez ce fichier après les tests</p>
            <p><strong>📞 Support :</strong> Consultez la documentation dans le dossier SETUP_CLIENT</p>
        </div>
    </div>
</body>
</html>
