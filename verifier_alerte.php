<?php
$pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$delai = 30; // jours
$aujourdhui = new DateTime();

// Récupérer toutes les entreprises avec leurs factures et virements
$sql = "SELECT e.id AS entreprise_id, e.email, e.chef 
        FROM entreprises e 
        WHERE e.active = 1";
$entreprises = $pdo->query($sql);

while ($entreprise = $entreprises->fetch(PDO::FETCH_ASSOC)) {
  $entreprise_id = $entreprise['entreprise_id'];

  // Total des factures avec statut non payé ou toutes les factures si la colonne n'existe pas
  $factureQuery = $pdo->prepare("
    SELECT f.*, 
           CASE 
             WHEN f.dateFin < CURDATE() THEN 1 
             ELSE 0 
           END as est_en_retard
    FROM facture f 
    WHERE f.entreprise_id = ? 
    AND (f.statut_paiement = 'non payé' OR f.statut_paiement IS NULL)
  ");
  $factureQuery->execute([$entreprise_id]);
  $factures = $factureQuery->fetchAll(PDO::FETCH_ASSOC);

  // Total des virements
  $virementQuery = $pdo->prepare("SELECT COALESCE(SUM(Montant), 0) as total FROM virement WHERE entreprise_id = ?");
  $virementQuery->execute([$entreprise_id]);
  $sommeVirements = $virementQuery->fetch(PDO::FETCH_ASSOC)['total'];

  $sommeFactures = 0;
  $dateFactureEnRetard = null;
  $numFactRetard = null;
  $hasFactureEnRetard = false;

  foreach ($factures as $f) {
    $sommeFactures += $f['Montant'];

    // Vérifier si la facture est en retard (période terminée)
    if ($f['est_en_retard'] == 1) {
      $hasFactureEnRetard = true;
      if (!$dateFactureEnRetard) {
        $dateFactureEnRetard = $f['dateEmission'] ?? $f['dateFin'];
        $numFactRetard = $f['numFact'];
      }
    }
  }

  $soldeRestant = $sommeFactures - $sommeVirements;

  // S'il reste de l'argent à payer ET il y a une facture en retard
  if ($soldeRestant > 0 && $hasFactureEnRetard && !empty($entreprise['email'])) {
    $to = $entreprise['email'];
    $sujet = "⏰ Facture en retard - Cyber Parc Djerba";
    $message = "Bonjour " . $entreprise['chef'] . ",\n\n"
      . "Votre facture N°" . $numFactRetard . " est en retard de paiement.\n"
      . "Montant restant à payer : " . number_format($soldeRestant, 2, ',', ' ') . " TND.\n"
      . "Merci de procéder au paiement dès que possible.\n\n"
      . "Cordialement,\nCyber Parc Djerba";

    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    // Envoyer l'email
    if (mail($to, $sujet, $message, $headers)) {
      error_log("Email d'alerte envoyé à {$entreprise['email']} pour l'entreprise ID {$entreprise_id}");
    } else {
      error_log("Erreur lors de l'envoi de l'email à {$entreprise['email']} pour l'entreprise ID {$entreprise_id}");
    }
  }
}

echo "Vérification des alertes terminée à " . date('Y-m-d H:i:s') . "\n";
?>
