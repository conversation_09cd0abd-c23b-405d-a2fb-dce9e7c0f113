@echo off
chcp 65001 >nul
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  📦 PACKAGE CLIENT BUREAU 📦                ║
echo ║                     CyberParc - v1.0                        ║
echo ║              Création directe sur le bureau                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 Ce script va créer le package client directement sur votre bureau
echo.
echo 📍 Emplacement : %USERPROFILE%\Desktop\CYBERPARC_CLIENT_PACKAGE
echo 📧 Archive ZIP : %USERPROFILE%\Desktop\CyberParc_Client_Package.zip
echo.

set /p confirm="Continuer ? (O/N) : "
if /i not "%confirm%"=="O" if /i not "%confirm%"=="Y" (
    echo ❌ Opération annulée
    pause
    exit /b
)

echo.
echo 🚀 Lancement de la création du package...
echo.

:: A<PERSON><PERSON> le script principal
call "CREER_PACKAGE_CLIENT.bat"

echo.
echo ✅ TERMINÉ !
echo.
echo 📂 Ouvrir le bureau pour voir le résultat...
start "" "%USERPROFILE%\Desktop"

echo.
echo 🎉 Votre package client est prêt sur le bureau !
echo.
echo 📧 Pour envoyer à votre client :
echo    1. Utilisez l'archive ZIP créée
echo    2. Ou compressez manuellement le dossier
echo    3. <PERSON><PERSON><PERSON> le modèle d'email fourni
echo.

pause
