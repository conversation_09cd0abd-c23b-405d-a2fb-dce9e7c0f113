@echo off
chcp 65001 >nul
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║           📦 CRÉATION ZIP CLIENT BUREAU 📦                  ║
echo ║              CyberParc - Package Compressé                  ║
echo ║            Création et déplacement automatique              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Définir les chemins
set "DESKTOP=%USERPROFILE%\Desktop"
set "TEMP_DIR=%TEMP%\CYBERPARC_TEMP"
set "PACKAGE_DIR=%TEMP_DIR%\CYBERPARC_CLIENT_PACKAGE"
set "ZIP_FINAL=%DESKTOP%\CyberParc_Client_Package.zip"

echo 🎯 Création d'un package ZIP directement sur le bureau
echo.
echo 📍 Bureau : %DESKTOP%
echo 📧 Archive finale : %ZIP_FINAL%
echo.

:: Vérifier qu'on est dans le bon dossier
if not exist "index.php" (
    echo ❌ ERREUR : Vous n'êtes pas dans le dossier du projet CyberParc !
    echo.
    echo 💡 Naviguez vers le dossier contenant :
    echo    - index.php, accueil.php
    echo    - config/database.php
    echo    - SETUP_CLIENT/
    echo.
    pause
    exit /b 1
)

echo ✅ Dossier du projet CyberParc détecté
echo.

set /p confirm="Créer le ZIP sur le bureau ? (O/N) : "
if /i not "%confirm%"=="O" if /i not "%confirm%"=="Y" (
    echo ❌ Opération annulée
    pause
    exit /b
)

echo.
echo 🚀 Création du package temporaire...
echo.

:: Nettoyer le dossier temporaire
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"
mkdir "%PACKAGE_DIR%"

:: Créer la structure
echo 📁 Création de la structure...
mkdir "%PACKAGE_DIR%\config"
mkdir "%PACKAGE_DIR%\uploads"
mkdir "%PACKAGE_DIR%\uploads\logos"
mkdir "%PACKAGE_DIR%\uploads\recus"
mkdir "%PACKAGE_DIR%\vendor"
mkdir "%PACKAGE_DIR%\DOCUMENTATION"
mkdir "%PACKAGE_DIR%\OUTILS"

:: Copier tous les fichiers
echo 📄 Copie des fichiers système...
copy "*.php" "%PACKAGE_DIR%\" >nul 2>&1
copy "*.ico" "%PACKAGE_DIR%\" >nul 2>&1
copy "*.jpg" "%PACKAGE_DIR%\" >nul 2>&1
copy "composer.json" "%PACKAGE_DIR%\" >nul 2>&1
copy "composer.lock" "%PACKAGE_DIR%\" >nul 2>&1

echo 🗄️ Copie de la base de données...
copy "SETUP_CLIENT\stage.sql" "%PACKAGE_DIR%\" >nul

echo ⚙️ Copie de la configuration...
copy "SETUP_CLIENT\database.php" "%PACKAGE_DIR%\config\" >nul

echo 📚 Copie de la documentation...
copy "SETUP_CLIENT\README.md" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\GUIDE_RAPIDE.html" "%PACKAGE_DIR%\" >nul
copy "SETUP_CLIENT\CHECKLIST.md" "%PACKAGE_DIR%\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\DOCUMENTATION_COMPLETE.md" "%PACKAGE_DIR%\DOCUMENTATION\" >nul
copy "SETUP_CLIENT\RACCOURCIS_ADMIN.html" "%PACKAGE_DIR%\DOCUMENTATION\" >nul

echo 🛠️ Copie des outils...
copy "SETUP_CLIENT\verifier_installation.php" "%PACKAGE_DIR%\OUTILS\" >nul
copy "SETUP_CLIENT\test_final.php" "%PACKAGE_DIR%\OUTILS\" >nul
copy "SETUP_CLIENT\INSTALLATION_RAPIDE.bat" "%PACKAGE_DIR%\" >nul

:: Copier vendor si disponible
if exist "vendor" (
    echo 📦 Copie des dépendances...
    xcopy "vendor" "%PACKAGE_DIR%\vendor" /e /i /q >nul 2>&1
    echo ✅ Dépendances copiées
)

:: Créer les fichiers d'aide
echo 📝 Création des fichiers d'aide...

:: LISEZ-MOI principal
(
echo # 🚀 CyberParc - Système de Gestion Comptable
echo ## Package Client Professionnel - Version 1.0
echo.
echo ### 🚀 Installation Express ^(5 minutes^)
echo.
echo **Étape 1 : Base de données**
echo 1. Ouvrez phpMyAdmin
echo 2. Créez une base de données nommée `stage`
echo 3. Importez le fichier `stage.sql`
echo.
echo **Étape 2 : Configuration**
echo 1. Modifiez le fichier `config/database.php`
echo 2. Changez les paramètres de connexion MySQL
echo 3. Configurez l'email ^(optionnel^)
echo.
echo **Étape 3 : Premier test**
echo 1. Accédez à votre site web
echo 2. Connectez-vous avec : **<EMAIL>** / **admin123**
echo 3. **⚠️ CHANGEZ IMMÉDIATEMENT LE MOT DE PASSE !**
echo.
echo ### 📚 Aide Disponible
echo - **GUIDE_RAPIDE.html** : Guide visuel complet
echo - **OUTILS/verifier_installation.php** : Diagnostic automatique
echo - **DOCUMENTATION/** : Aide technique détaillée
echo.
echo ### 🔒 Sécurité
echo ⚠️ **CRITIQUE** : Changez le mot de passe administrateur dès la première connexion !
echo.
echo ### 📞 Support
echo Consultez la documentation incluse pour toute assistance technique.
echo.
echo ---
echo **CyberParc v1.0** - Système professionnel de gestion comptable  
echo **Date** : %DATE%  
echo **Support** : Documentation complète incluse
) > "%PACKAGE_DIR%\LISEZ-MOI.md"

:: Script d'installation express
(
echo @echo off
echo chcp 65001 ^>nul
echo color 0B
echo echo.
echo echo ╔══════════════════════════════════════════════════════════════╗
echo echo ║                    🚀 CYBERPARC SETUP 🚀                    ║
echo echo ║              Installation Express - 5 minutes               ║
echo echo ╚══════════════════════════════════════════════════════════════╝
echo echo.
echo echo 📋 SUIVEZ CES 5 ÉTAPES :
echo echo.
echo echo 1️⃣  **BASE DE DONNÉES**
echo echo     - Ouvrez phpMyAdmin
echo echo     - Créez une base nommée 'stage'
echo echo     - Importez le fichier 'stage.sql'
echo echo.
echo echo 2️⃣  **CONFIGURATION**
echo echo     - Ouvrez 'config\database.php'
echo echo     - Modifiez les paramètres MySQL
echo echo     - Sauvegardez le fichier
echo echo.
echo echo 3️⃣  **TEST**
echo echo     - Accédez à votre site web
echo echo     - La page de connexion doit s'afficher
echo echo.
echo echo 4️⃣  **CONNEXION**
echo echo     - Email : <EMAIL>
echo echo     - Mot de passe : admin123
echo echo.
echo echo 5️⃣  **SÉCURITÉ**
echo echo     - ⚠️ CHANGEZ LE MOT DE PASSE IMMÉDIATEMENT !
echo echo     - Créez vos propres comptes utilisateur
echo echo.
echo echo 🔧 OUTILS DISPONIBLES :
echo echo    - GUIDE_RAPIDE.html ^(guide visuel^)
echo echo    - OUTILS\verifier_installation.php ^(diagnostic^)
echo echo    - test_rapide.php ^(vérification express^)
echo echo.
echo echo 📞 En cas de problème, consultez LISEZ-MOI.md
echo echo.
echo pause
echo echo.
echo echo ✨ Installation terminée ! Félicitations !
echo pause
) > "%PACKAGE_DIR%\INSTALLATION_EXPRESS.bat"

:: Test rapide
(
echo ^<?php
echo // Test Express - CyberParc
echo echo "^<h1 style='color:#2c3e50;text-align:center'^>🔍 Test Express CyberParc^</h1^>";
echo echo "^<div style='font-family:Arial,sans-serif;max-width:600px;margin:20px auto;padding:20px;border:1px solid #ddd;border-radius:10px'^>";
echo if ^(file_exists^('config/database.php'^)^) {
echo     echo "^<p style='color:green;font-weight:bold'^>✅ Configuration trouvée^</p^>";
echo     require_once 'config/database.php';
echo     try {
echo         $pdo = new PDO^($dsn, $username, $password, $options^);
echo         echo "^<p style='color:green;font-weight:bold'^>✅ Connexion base de données réussie^</p^>";
echo         echo "^<div style='background:#e8f5e8;padding:20px;border-radius:8px;text-align:center'^>";
echo         echo "^<h2 style='color:#27ae60'^>🎉 Installation Réussie !^</h2^>";
echo         echo "^<p^>^<a href='index.php' style='background:#3498db;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;font-weight:bold'^>👉 Accéder à CyberParc^</a^>^</p^>";
echo         echo "^<p style='margin-top:15px;font-size:14px;color:#666'^>Connexion : <EMAIL> / admin123^</p^>";
echo         echo "^</div^>";
echo     } catch ^(Exception $e^) {
echo         echo "^<p style='color:red;font-weight:bold'^>❌ Erreur BDD: " . htmlspecialchars^($e-^>getMessage^(^)^) . "^</p^>";
echo         echo "^<p^>Vérifiez les paramètres dans config/database.php^</p^>";
echo     }
echo } else {
echo     echo "^<p style='color:red;font-weight:bold'^>❌ Configuration manquante^</p^>";
echo     echo "^<p^>Configurez le fichier config/database.php^</p^>";
echo }
echo echo "^</div^>";
echo ?^>
) > "%PACKAGE_DIR%\test_rapide.php"

:: Créer l'archive ZIP
echo.
echo 🗜️ Création de l'archive ZIP...

:: Supprimer l'ancienne archive si elle existe
if exist "%ZIP_FINAL%" del "%ZIP_FINAL%"

:: Essayer avec PowerShell (disponible sur Windows 10+)
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%\*' -DestinationPath '%ZIP_FINAL%' -Force" >nul 2>&1

if exist "%ZIP_FINAL%" (
    echo ✅ Archive ZIP créée avec succès !
    echo 📧 Emplacement : %ZIP_FINAL%
) else (
    echo ⚠️ Création ZIP échouée - Compression manuelle nécessaire
    echo 💡 Vous pouvez compresser manuellement le dossier créé
)

:: Nettoyer le dossier temporaire
rmdir /s /q "%TEMP_DIR%"

echo.
echo ✅ PACKAGE ZIP CRÉÉ SUR LE BUREAU !
echo.
echo 📍 Emplacement final : %ZIP_FINAL%
echo 📊 Contenu : Système CyberParc complet
echo 📧 Prêt à envoyer : OUI
echo.

:: Ouvrir le bureau pour montrer le fichier
echo 🚀 Ouverture du bureau...
start "" "%DESKTOP%"

:: Afficher les informations du fichier créé
if exist "%ZIP_FINAL%" (
    echo.
    echo 📊 Informations de l'archive :
    for %%A in ("%ZIP_FINAL%") do (
        echo    📧 Nom : %%~nxA
        echo    📏 Taille : %%~zA octets
        echo    📅 Date : %%~tA
    )
)

echo.
echo 🎯 PRÊT POUR ENVOI CLIENT !
echo.
echo 💌 Instructions d'envoi :
echo    1. L'archive ZIP est maintenant sur votre bureau
echo    2. Joignez-la à votre email client
echo    3. Incluez les instructions d'installation
echo    4. Envoyez !
echo.

echo 📧 Message type pour le client :
echo.
echo "Bonjour [Nom],
echo Voici votre système CyberParc prêt à installer !
echo 
echo 🚀 Installation : 5 minutes
echo 1. Décompressez l'archive
echo 2. Double-cliquez sur INSTALLATION_EXPRESS.bat
echo 3. Suivez les étapes
echo 
echo 🔐 Connexion : <EMAIL> / admin123
echo ⚠️ Changez le mot de passe immédiatement !
echo 
echo Cordialement"
echo.

pause
echo.
echo 🎉 Archive ZIP créée et déplacée sur le bureau avec succès !
pause
