<?php
session_start();
$local = $_SESSION['utilisateur']['local'] ?? '';
if (empty($local)) {
  die("Local non défini. Veuillez vous connecter.");
}
try {
  $pdo = new PDO('mysql:host=localhost;dbname=stage;charset=utf8', 'root', '');
  $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
  die("Erreur base de données : " . $e->getMessage());
}

$utilisateurId = $_SESSION['utilisateur']['id'] ?? null;
if (!$utilisateurId) {
  die("Utilisateur non connecté");
}

// --- LOGIQUE DE CALCUL DU SOLDE IMPAYÉ INTÉGRÉE ---
// Récupérer toutes les entreprises avec un solde impayé et les stocker dans un tableau de recherche rapide
$stmtOutstandingBalance = $pdo->prepare("
  SELECT
      e.id AS entreprise_id,
      e.nom AS entreprise_nom,
      COALESCE(SUM(f.Montant), 0) AS total_factures,
      (
          SELECT COALESCE(SUM(v.Montant), 0)
          FROM virement v
          WHERE v.entreprise_id = e.id
      ) AS total_virements
  FROM
      entreprises e
  JOIN
      facture f ON e.id = f.entreprise_id
  WHERE
      f.statut_paiement = 'non payé' AND f.dateFin < CURDATE()
      AND e.local = ?
  GROUP BY
      e.id, e.nom
  HAVING
      (total_factures - total_virements) > 0");
$stmtOutstandingBalance->execute([$local]);
$companiesWithOutstandingBalance = $stmtOutstandingBalance->fetchAll(PDO::FETCH_ASSOC);

$companiesOutstandingBalanceLookup = [];
foreach ($companiesWithOutstandingBalance as $company) {
  $companiesOutstandingBalanceLookup[$company['entreprise_id']] = (float)($company['total_factures'] - $company['total_virements']);
}
// --- FIN LOGIQUE DE CALCUL DU SOLDE IMPAYÉ INTÉGRÉE ---

// Récupérer toutes les notifications de la base de données pour l'utilisateur
$stmtDbNotifications = $pdo->prepare("
  SELECT
      n.*,
      e.id AS entreprise_id,
      e.nom AS entreprise_nom,
      COALESCE(n.date_creation, n.timestamp, NOW()) AS effective_date
  FROM
      notifications n
  LEFT JOIN
      entreprises e ON n.entreprise_id = e.id
  WHERE
      n.utilisateur_id = ?
  ORDER BY
      effective_date DESC");
$stmtDbNotifications->execute([$utilisateurId]);
$dbNotifications = $stmtDbNotifications->fetchAll(PDO::FETCH_ASSOC);

$allNotifications = $dbNotifications; // Commencer avec les notifications de la base de données

// Fonction pour déterminer le trimestre à partir des dates
function getTrimestreFromDates($dateDebut, $dateFin) {
  if (!$dateDebut || !$dateFin) return "Période inconnue";
  
  $debut = new DateTime($dateDebut);
  $fin = new DateTime($dateFin);
  $annee = $debut->format('Y');
  
  $moisDebut = (int)$debut->format('n');
  $moisFin = (int)$fin->format('n');
  
  // Déterminer le trimestre basé sur les mois
  if ($moisDebut == 1 && $moisFin == 3) {
    return "T1 $annee";
  } elseif ($moisDebut == 4 && $moisFin == 6) {
    return "T2 $annee";
  } elseif ($moisDebut == 7 && $moisFin == 9) {
    return "T3 $annee";
  } elseif ($moisDebut == 10 && $moisFin == 12) {
    return "T4 $annee";
  } else {
    // Période personnalisée
    return $debut->format('d/m/Y') . ' au ' . $fin->format('d/m/Y');
  }
}

// Ajouter les alertes dynamiques pour les entreprises qui *ont actuellement* un solde impayé
foreach ($companiesWithOutstandingBalance as $company) {
  $soldeRestant = (float)($company['total_factures'] - $company['total_virements']);
  $message = "🚨 ALERTE : L'entreprise " . htmlspecialchars($company['entreprise_nom']) . " a un solde restant dû de " . number_format($soldeRestant, 2, ',', ' ') . " TND pour des factures en retard.";
  $allNotifications[] = [
    'id' => 'company_outstanding_balance_alert_' . $company['entreprise_id'],
    'type' => 'company_outstanding_balance_alert',
    'message' => $message,
    'effective_date' => (new DateTime())->format('Y-m-d H:i:s'),
    'vu' => 0,
    'entreprise_id' => $company['entreprise_id'],
    'facture_id' => null,
    'entreprise_nom' => $company['entreprise_nom']
  ];
}

// Filtrer les notifications en fonction du solde impayé et du statut lu/non lu
$filter = $_GET['filter'] ?? 'all';
$filteredNotifications = [];

foreach ($allNotifications as $notif) {
  $includeNotification = true;

  // Règle : Si la notification est liée à une entreprise et que cette entreprise n'a PAS de solde impayé, l'exclure.
  // Cette règle s'applique aux notifications qui ont un 'entreprise_id' et qui NE SONT PAS les alertes dynamiques de solde impayé.
  // Les alertes dynamiques sont déjà générées uniquement s'il y a un solde, donc elles n'ont pas besoin de ce filtre.
  if (!empty($notif['entreprise_id']) && $notif['type'] !== 'company_outstanding_balance_alert') {
    $companyId = $notif['entreprise_id'];
    // Vérifier si l'entreprise n'a pas de solde impayé dans notre tableau de recherche
    // Si l'ID de l'entreprise n'est pas dans le tableau, ou si son solde est <= 0, alors elle n'a pas de solde impayé.
    if (!isset($companiesOutstandingBalanceLookup[$companyId]) || $companiesOutstandingBalanceLookup[$companyId] <= 0) {
      $includeNotification = false;
    }
  }

  // Appliquer le filtre existant (toutes, lues, non lues)
  if ($includeNotification) { // Seulement si la notification n'a pas déjà été exclue par la règle du solde
    if (
      $filter === 'all' ||
      ($filter === 'unread' && $notif['vu'] == 0) ||
      ($filter === 'read' && $notif['vu'] == 1)
    ) {
      // La notification est incluse
    } else {
      $includeNotification = false; // Exclure si elle ne correspond pas au filtre lu/non lu
    }
  }

  if ($includeNotification) {
    $filteredNotifications[] = $notif;
  }
}

// Tri par date décroissante
usort($filteredNotifications, function ($a, $b) {
  $timeA = strtotime($a['effective_date']);
  $timeB = strtotime($b['effective_date']);
  if ($timeA === false) $timeA = 0;
  if ($timeB === false) $timeB = 0;
  return $timeB - $timeA;
});

$message = $_GET['message'] ?? '';
$messageType = $_GET['type'] ?? '';
?>
<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8">
  <title>Gestion des Notifications | Cyber Parc Djerba</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Styles existants */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, #1e3c72, #2a5298);
      color: white;
      min-height: 100vh;
    }

    header {
      background-color: rgba(0, 0, 0, 0.3);
      padding: 1rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      backdrop-filter: blur(10px);
    }

    h1 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 700;
    }

    .back-btn {
      color: #fff;
      text-decoration: none;
      font-weight: 600;
      background: #6b7280;
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .back-btn:hover {
      background: #4b5563;
      transform: translateY(-2px);
    }

    .container {
      position: relative;
      z-index: 1;
      max-width: 1000px;
      margin: 2rem auto;
      padding: 2rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
    }

    .btn {
      background: linear-gradient(135deg, #1e90ff, #1e3c72);
      color: white;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 12px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
    }

    .btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(30, 144, 255, 0.3);
    }

    .btn-secondary {
      background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .btn-danger {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .btn-success {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    .btn-small {
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
    }

    .btn-info {
      background: linear-gradient(135deg, #3b82f6, #2563eb);
      color: white;
    }

    .btn-info:hover {
      box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
    }

    .btn-warning {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }

    .btn-warning:hover {
      box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
    }

    .alert {
      padding: 1rem;
      border-radius: 12px;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
    }

    .alert-success {
      background: #f0fdf4;
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    .alert-danger {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .notification-list {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      color: #374151;
    }

    .notification-item {
      padding: 1rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
    }

    .notification-item:last-child {
      border-bottom: none;
    }

    .notification-item.read {
      background-color: #f9fafb;
      opacity: 0.7;
    }

    .notification-item.unread {
      background-color: #fff;
      font-weight: 600;
    }

    .notification-content {
      flex-grow: 1;
    }

    .notification-message {
      margin-bottom: 0.25rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .notification-date {
      font-size: 0.8rem;
      color: #6b7280;
    }

    .notification-actions {
      display: flex;
      gap: 0.5rem;
      flex-shrink: 0;
      flex-wrap: wrap;
      justify-content: flex-end;
    }

    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      color: #6b7280;
    }

    .empty-state-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .filter-controls {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .filter-controls select {
      padding: 0.5rem 1rem;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      background: white;
      color: #374151;
      font-family: 'Inter', sans-serif;
    }

    @media (max-width: 768px) {
      .container {
        margin: 1rem;
        padding: 1.5rem;
      }

      .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
      }

      .filter-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .notification-item {
        flex-direction: column;
        align-items: flex-start;
      }

      .notification-actions {
        margin-top: 0.75rem;
        width: 100%;
        justify-content: flex-end;
      }

      .notification-message a {
        color: inherit;
        text-decoration: none;
      }

      .notification-message a:hover {
        text-decoration: underline;
      }
    }
  </style>
</head>

<body>
  <header>
    <h1>🔔 Gestion des Notifications</h1>
    <a href="accueil.php" class="back-btn">← Retour à l'accueil</a>
  </header>
  <div class="container">
    <?php if ($message): ?>
      <div class="alert alert-<?= htmlspecialchars($messageType) ?>">
        <span><?= $messageType === 'success' ? '✅' : '❌' ?></span>
        <?= htmlspecialchars($message) ?>
      </div>
    <?php endif; ?>
    <div class="section-header">
      <h2 class="section-title">Toutes vos notifications (<?= count($filteredNotifications) ?>)</h2>
      <div class="filter-controls">
        <select id="notificationFilter" onchange="applyFilter()">
          <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>Toutes</option>
          <option value="unread" <?= $filter === 'unread' ? 'selected' : '' ?>>Non lues</option>
          <option value="read" <?= $filter === 'read' ? 'selected' : '' ?>>Lues</option>
        </select>
        <button class="btn btn-success btn-small" onclick="markAllAsRead()">
          ✅ Tout marquer comme lu
        </button>
        <button class="btn btn-danger btn-small" onclick="deleteAllRead()">
          🗑️ Supprimer les lues
        </button>
      </div>
    </div>
    <div class="notification-list">
      <?php if (empty($filteredNotifications)): ?>
        <div class="empty-state">
          <div class="empty-state-icon">🎉</div>
          <h3>Aucune notification à afficher</h3>
          <p>Vous êtes à jour !</p>
        </div>
      <?php else: ?>
        <?php foreach ($filteredNotifications as $notif): ?>
          <div class="notification-item <?= $notif['vu'] == 1 ? 'read' : 'unread' ?>" id="notif-<?= $notif['id'] ?>">
            <div class="notification-content">
              <div class="notification-message">
                <?php
                $messageContent = htmlspecialchars($notif['message']);
                $hasEntrepriseId = !empty($notif['entreprise_id']);
                $hasFactureId = !empty($notif['facture_id']);
                $isCompanyOutstandingAlert = ($notif['type'] === 'company_outstanding_balance_alert');
                if ($isCompanyOutstandingAlert && $hasEntrepriseId) {
                  echo '<a href="details_entreprise.php?id=' . $notif['entreprise_id'] . '" class="notification-link">' . $messageContent . ' ➡ Voir l\'entreprise</a>';
                } elseif ($hasFactureId) {
                  echo '<a href="details_facture.php?id=' . $notif['facture_id'] . '" class="notification-link">' . $messageContent . ' ➡ Voir la facture</a>';
                } elseif ($hasEntrepriseId) {
                  echo '<a href="details_entreprise.php?id=' . $notif['entreprise_id'] . '" class="notification-link">' . $messageContent . ' ➡ Voir l\'entreprise</a>';
                } else {
                  echo $messageContent;
                }
                ?>
              </div>
              <div class="notification-date">
                <?= date('d/m/Y H:i', strtotime($notif['effective_date'])) ?>
              </div>
            </div>
            <div class="notification-actions">
              <?php if ($notif['vu'] == 0): ?>
                <button class="btn btn-small btn-success" onclick="markNotification(<?= is_string($notif['id']) ? "'{$notif['id']}'" : $notif['id'] ?>, 'read')">
                  Marquer lu
                </button>
              <?php else: ?>
                <button class="btn btn-small btn-secondary" onclick="markNotification(<?= is_string($notif['id']) ? "'{$notif['id']}'" : $notif['id'] ?>, 'unread')">
                  Marquer non lu
                </button>
              <?php endif; ?>
              <button class="btn btn-small btn-danger" onclick="deleteNotification(<?= is_string($notif['id']) ? "'{$notif['id']}'" : $notif['id'] ?>)">
                Supprimer
              </button>
            </div>
          </div>
        <?php endforeach; ?>
      <?php endif; ?>
    </div>
  </div>
  <script>
    // Fonction pour charger les statuts des notifications dynamiques depuis localStorage
    function loadDynamicNotificationStates() {
      const states = JSON.parse(localStorage.getItem('dynamicNotificationStates') || '{}');
      for (const id in states) {
        const item = document.getElementById(`notif-${id}`);
        if (item) {
          if (states[id] === 'read') {
            item.classList.remove('unread');
            item.classList.add('read');
            // Mettre à jour le texte du bouton
            const button = item.querySelector('.btn-success, .btn-secondary');
            if (button) {
              button.classList.remove('btn-success');
              button.classList.add('btn-secondary');
              button.textContent = '↺ Non lu';
              button.onclick = () => markNotification(id, 'unread');
            }
          } else {
            item.classList.remove('read');
            item.classList.add('unread');
            // Mettre à jour le texte du bouton
            const button = item.querySelector('.btn-success, .btn-secondary');
            if (button) {
              button.classList.remove('btn-secondary');
              button.classList.add('btn-success');
              button.textContent = '✓ Marquer lu';
              button.onclick = () => markNotification(id, 'read');
            }
          }
        }
      }
    }
    // Fonction pour sauvegarder le statut d'une notification dynamique dans localStorage
    function saveDynamicNotificationState(id, status) {
      const states = JSON.parse(localStorage.getItem('dynamicNotificationStates') || '{}');
      states[id] = status;
      localStorage.setItem('dynamicNotificationStates', JSON.stringify(states));
    }
    // Fonction pour supprimer une notification dynamique de localStorage
    function deleteDynamicNotificationState(id) {
      const states = JSON.parse(localStorage.getItem('dynamicNotificationStates') || '{}');
      delete states[id];
      localStorage.setItem('dynamicNotificationStates', JSON.stringify(states));
    }
    // Appliquer les statuts au chargement de la page
    document.addEventListener('DOMContentLoaded', loadDynamicNotificationStates);

    function applyFilter() {
      const filterValue = document.getElementById('notificationFilter').value;
      window.location.href = `notifications.php?filter=${filterValue}`;
    }

    async function markNotification(id, status) {
      // Handle dynamic IDs (e.g., 'company_outstanding_balance_alert_123') which are strings
      const isDynamicId = typeof id === 'string' && id.startsWith('company_outstanding_balance_alert_');
      const action = status === 'read' ? 'mark_as_read' : 'mark_as_unread';

      if (isDynamicId) {
        // Pour les notifications dynamiques, mettez à jour le DOM et localStorage
        const item = document.getElementById(`notif-${id}`);
        if (item) {
          if (status === 'read') {
            item.classList.remove('unread');
            item.classList.add('read');
            // Mettre à jour le bouton
            const button = item.querySelector('.btn-success');
            if (button) {
              button.classList.remove('btn-success');
              button.classList.add('btn-secondary');
              button.textContent = '↺ Non lu';
              button.onclick = () => markNotification(id, 'unread');
            }
          } else {
            item.classList.remove('read');
            item.classList.add('unread');
            // Mettre à jour le bouton
            const button = item.querySelector('.btn-secondary');
            if (button) {
              button.classList.remove('btn-secondary');
              button.classList.add('btn-success');
              button.textContent = '✓ Marquer lu';
              button.onclick = () => markNotification(id, 'read');
            }
          }
          saveDynamicNotificationState(id, status); // Sauvegarder le statut dans localStorage
        }
        // Pas de rechargement de page pour les notifications dynamiques
        return;
      }

      // Pour les notifications de la base de données, utilisez fetch et rechargez la page
      const response = await fetch('manage_notifications_action.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: id,
          action: action
        })
      });
      const data = await response.json();
      if (data.success) {
        window.location.reload(); // Recharger la page pour refléter le changement
      } else {
        alert('Erreur: ' + data.message);
      }
    }

    async function deleteNotification(id) {
      if (!confirm('Êtes-vous sûr de vouloir supprimer cette notification ?')) {
        return;
      }
      const isDynamicId = typeof id === 'string' && id.startsWith('company_outstanding_balance_alert_');
      if (isDynamicId) {
        // Pour les notifications dynamiques, supprimez du DOM et localStorage
        const item = document.getElementById(`notif-${id}`);
        if (item) {
          item.remove();
          deleteDynamicNotificationState(id); // Supprimer de localStorage
        }
        return;
      }
      const response = await fetch('manage_notifications_action.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: id,
          action: 'delete'
        })
      });
      const data = await response.json();
      if (data.success) {
        window.location.reload(); // Recharger la page
      } else {
        alert('Erreur: ' + data.message);
      }
    }

    async function markAllAsRead() {
      if (!confirm('Voulez-vous marquer toutes les notifications comme lues ?')) {
        return;
      }
      // Marquer toutes les notifications de la DB comme lues
      const response = await fetch('manage_notifications_action.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'mark_all_as_read'
        })
      });
      const data = await response.json();

      // Marquer toutes les notifications dynamiques comme lues dans localStorage et DOM
      const dynamicNotifications = document.querySelectorAll('[id^="notif-company_outstanding_balance_alert_"]');
      dynamicNotifications.forEach(item => {
        const id = item.id.replace('notif-', '');
        item.classList.remove('unread');
        item.classList.add('read');
        saveDynamicNotificationState(id, 'read');
        // Mettre à jour le bouton
        const button = item.querySelector('.btn-success');
        if (button) {
          button.classList.remove('btn-success');
          button.classList.add('btn-secondary');
          button.textContent = '↺ Non lu';
          button.onclick = () => markNotification(id, 'unread');
        }
      });

      if (data.success) {
        window.location.reload(); // Recharger la page pour refléter les changements de la DB et du filtre
      } else {
        alert('Erreur: ' + data.message);
      }
    }

    async function deleteAllRead() {
      if (!confirm('Voulez-vous supprimer toutes les notifications lues ? Cette action est irréversible.')) {
        return;
      }
      // Supprimer toutes les notifications lues de la DB
      const response = await fetch('manage_notifications_action.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'delete_all_read'
        })
      });
      const data = await response.json();

      // Supprimer toutes les notifications dynamiques lues de localStorage et DOM
      const dynamicNotifications = document.querySelectorAll('[id^="notif-company_outstanding_balance_alert_"].read');
      dynamicNotifications.forEach(item => {
        const id = item.id.replace('notif-', '');
        item.remove();
        deleteDynamicNotificationState(id);
      });

      if (data.success) {
        window.location.reload(); // Recharger la page pour refléter les changements de la DB et du filtre
      } else {
        alert('Erreur: ' + data.message);
      }
    }
  </script>
</body>

</html>
